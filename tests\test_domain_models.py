"""
Tests for domain models
"""
import pytest
from pydantic import ValidationError

from app.models.domain_models import (
    DomainCheckRequest, DomainResult, JobProgress, 
    JobStatus, BatchInfo, WebSocketMessage
)

class TestDomainCheckRequest:
    """Test DomainCheckRequest model"""
    
    def test_valid_request(self):
        """Test valid domain check request"""
        request = DomainCheckRequest(
            domains=["example.com", "test.org", "sample.net"],
            job_name="Test Job"
        )
        
        assert len(request.domains) == 3
        assert request.job_name == "Test Job"
        assert all(domain in request.domains for domain in ["example.com", "test.org", "sample.net"])
    
    def test_domain_validation(self):
        """Test domain validation"""
        # Valid domains
        valid_request = DomainCheckRequest(
            domains=["valid-domain.com", "another.org", "test123.net"]
        )
        assert len(valid_request.domains) == 3
        
        # Invalid domains should be filtered out
        mixed_request = DomainCheckRequest(
            domains=["valid.com", "invalid..domain", "", "  ", "another-valid.org"]
        )
        assert len(mixed_request.domains) == 2
        assert "valid.com" in mixed_request.domains
        assert "another-valid.org" in mixed_request.domains
    
    def test_empty_domains_error(self):
        """Test error when no domains provided"""
        with pytest.raises(ValidationError):
            DomainCheckRequest(domains=[])
    
    def test_too_many_domains_error(self):
        """Test error when too many domains provided"""
        domains = [f"domain{i}.com" for i in range(50001)]
        with pytest.raises(ValidationError):
            DomainCheckRequest(domains=domains)

class TestDomainResult:
    """Test DomainResult model"""
    
    def test_available_domain(self):
        """Test available domain result"""
        result = DomainResult(
            domain="available.com",
            available=True,
            price="$10.99",
            currency="USD",
            registrar="Namecheap"
        )
        
        assert result.domain == "available.com"
        assert result.available is True
        assert result.price == "$10.99"
        assert result.currency == "USD"
        assert result.registrar == "Namecheap"
        assert result.error is None
    
    def test_unavailable_domain(self):
        """Test unavailable domain result"""
        result = DomainResult(
            domain="taken.com",
            available=False
        )
        
        assert result.domain == "taken.com"
        assert result.available is False
        assert result.price is None
    
    def test_error_domain(self):
        """Test domain with error"""
        result = DomainResult(
            domain="error.com",
            error="Timeout during check"
        )
        
        assert result.domain == "error.com"
        assert result.available is None
        assert result.error == "Timeout during check"

class TestJobProgress:
    """Test JobProgress model"""
    
    def test_job_creation(self):
        """Test job progress creation"""
        job = JobProgress(
            job_id="test-job-123",
            job_name="Test Job",
            status=JobStatus.PENDING,
            total_domains=1000,
            total_batches=2
        )
        
        assert job.job_id == "test-job-123"
        assert job.job_name == "Test Job"
        assert job.status == JobStatus.PENDING
        assert job.total_domains == 1000
        assert job.total_batches == 2
        assert job.processed_domains == 0
        assert job.completed_batches == 0
        assert job.progress_percentage == 0.0
    
    def test_batch_info(self):
        """Test batch info"""
        batch = BatchInfo(
            batch_id="batch-1",
            total_domains=500,
            processed_domains=250,
            status=JobStatus.PROCESSING
        )
        
        assert batch.batch_id == "batch-1"
        assert batch.total_domains == 500
        assert batch.processed_domains == 250
        assert batch.status == JobStatus.PROCESSING

class TestWebSocketMessage:
    """Test WebSocket message model"""
    
    def test_progress_message(self):
        """Test progress WebSocket message"""
        message = WebSocketMessage(
            type="progress",
            job_id="job-123",
            data={"progress": 50, "status": "processing"},
            timestamp="2024-01-01T12:00:00"
        )
        
        assert message.type == "progress"
        assert message.job_id == "job-123"
        assert message.data["progress"] == 50
        assert message.timestamp == "2024-01-01T12:00:00"
    
    def test_completion_message(self):
        """Test completion WebSocket message"""
        message = WebSocketMessage(
            type="completed",
            job_id="job-123",
            data={"results_url": "/api/job/job-123/download"},
            timestamp="2024-01-01T12:30:00"
        )
        
        assert message.type == "completed"
        assert message.data["results_url"] == "/api/job/job-123/download"
