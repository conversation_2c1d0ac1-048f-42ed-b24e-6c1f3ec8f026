"""
CSV processing service for domain checking results
"""
import pandas as pd
import os
import asyncio
from typing import List, Dict, Optional
from pathlib import Path

from app.models.domain_models import DomainResult
from app.config import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)

class CSVProcessor:
    """Service for processing CSV files from domain checking"""
    
    def __init__(self):
        self.download_dir = Path(settings.download_dir)
        self.temp_dir = Path(settings.temp_dir)
        
        # Ensure directories exist
        self.download_dir.mkdir(parents=True, exist_ok=True)
        self.temp_dir.mkdir(parents=True, exist_ok=True)
    
    async def merge_csv_files(self, file_paths: List[str], output_filename: str) -> str:
        """Merge multiple CSV files into one"""
        try:
            logger.info(f"Merging {len(file_paths)} CSV files into {output_filename}")
            
            # Run in executor to avoid blocking
            loop = asyncio.get_event_loop()
            merged_file_path = await loop.run_in_executor(
                None, self._merge_csv_files_sync, file_paths, output_filename
            )
            
            logger.info(f"Successfully merged files into {merged_file_path}")
            return merged_file_path
            
        except Exception as e:
            logger.error(f"Error merging CSV files: {str(e)}")
            raise
    
    def _merge_csv_files_sync(self, file_paths: List[str], output_filename: str) -> str:
        """Synchronous CSV merging"""
        dataframes = []
        
        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    df = pd.read_csv(file_path)
                    dataframes.append(df)
                    logger.info(f"Loaded {len(df)} rows from {file_path}")
                else:
                    logger.warning(f"File not found: {file_path}")
            except Exception as e:
                logger.error(f"Error reading {file_path}: {str(e)}")
                continue
        
        if not dataframes:
            raise Exception("No valid CSV files to merge")
        
        # Merge all dataframes
        merged_df = pd.concat(dataframes, ignore_index=True)
        
        # Remove duplicates based on domain name
        if 'Domain' in merged_df.columns:
            merged_df = merged_df.drop_duplicates(subset=['Domain'], keep='first')
        elif 'domain' in merged_df.columns:
            merged_df = merged_df.drop_duplicates(subset=['domain'], keep='first')
        
        # Save merged file
        output_path = self.download_dir / output_filename
        merged_df.to_csv(output_path, index=False)
        
        logger.info(f"Merged {len(merged_df)} unique domains into {output_path}")
        return str(output_path)
    
    async def parse_results_file(self, file_path: str) -> List[DomainResult]:
        """Parse CSV results file into DomainResult objects"""
        try:
            logger.info(f"Parsing results file: {file_path}")
            
            # Run in executor to avoid blocking
            loop = asyncio.get_event_loop()
            results = await loop.run_in_executor(
                None, self._parse_results_file_sync, file_path
            )
            
            logger.info(f"Parsed {len(results)} domain results")
            return results
            
        except Exception as e:
            logger.error(f"Error parsing results file {file_path}: {str(e)}")
            raise
    
    def _parse_results_file_sync(self, file_path: str) -> List[DomainResult]:
        """Synchronous results file parsing"""
        try:
            df = pd.read_csv(file_path)
            results = []
            
            for _, row in df.iterrows():
                try:
                    # Map CSV columns to DomainResult fields
                    # Namecheap CSV typically has columns like: Domain, Available, Price, etc.
                    domain_result = DomainResult(
                        domain=self._get_column_value(row, ['Domain', 'domain', 'Domain Name']),
                        available=self._parse_availability(row),
                        price=self._get_column_value(row, ['Price', 'price', 'Cost']),
                        currency=self._get_column_value(row, ['Currency', 'currency'], default='USD'),
                        registrar=self._get_column_value(row, ['Registrar', 'registrar'], default='Namecheap')
                    )
                    results.append(domain_result)
                    
                except Exception as e:
                    logger.warning(f"Error parsing row: {str(e)}")
                    continue
            
            return results
            
        except Exception as e:
            logger.error(f"Error in _parse_results_file_sync: {str(e)}")
            raise
    
    def _get_column_value(self, row, column_names: List[str], default=None):
        """Get value from row using multiple possible column names"""
        for col_name in column_names:
            if col_name in row and pd.notna(row[col_name]):
                return str(row[col_name]).strip()
        return default
    
    def _parse_availability(self, row) -> Optional[bool]:
        """Parse availability from various possible column formats"""
        # Try different column names for availability
        availability_columns = ['Available', 'available', 'Availability', 'Status', 'status']
        
        for col_name in availability_columns:
            if col_name in row and pd.notna(row[col_name]):
                value = str(row[col_name]).lower().strip()
                
                # Common availability indicators
                if value in ['true', 'yes', 'available', '1', 'y']:
                    return True
                elif value in ['false', 'no', 'unavailable', 'taken', '0', 'n']:
                    return False
        
        return None
    
    async def export_results_to_csv(self, results: List[DomainResult], filename: str) -> str:
        """Export DomainResult objects to CSV file"""
        try:
            logger.info(f"Exporting {len(results)} results to {filename}")
            
            # Run in executor to avoid blocking
            loop = asyncio.get_event_loop()
            file_path = await loop.run_in_executor(
                None, self._export_results_to_csv_sync, results, filename
            )
            
            logger.info(f"Successfully exported results to {file_path}")
            return file_path
            
        except Exception as e:
            logger.error(f"Error exporting results to CSV: {str(e)}")
            raise
    
    def _export_results_to_csv_sync(self, results: List[DomainResult], filename: str) -> str:
        """Synchronous CSV export"""
        # Convert results to DataFrame
        data = []
        for result in results:
            data.append({
                'Domain': result.domain,
                'Available': result.available,
                'Price': result.price,
                'Currency': result.currency,
                'Registrar': result.registrar,
                'Error': result.error
            })
        
        df = pd.DataFrame(data)
        
        # Save to file
        output_path = self.download_dir / filename
        df.to_csv(output_path, index=False)
        
        return str(output_path)
    
    def cleanup_temp_files(self, file_paths: List[str]):
        """Clean up temporary files"""
        for file_path in file_paths:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.info(f"Cleaned up temp file: {file_path}")
            except Exception as e:
                logger.warning(f"Error cleaning up {file_path}: {str(e)}")
    
    def get_file_info(self, file_path: str) -> Dict:
        """Get information about a CSV file"""
        try:
            if not os.path.exists(file_path):
                return {"error": "File not found"}
            
            df = pd.read_csv(file_path)
            
            return {
                "file_path": file_path,
                "file_size": os.path.getsize(file_path),
                "row_count": len(df),
                "column_count": len(df.columns),
                "columns": list(df.columns),
                "sample_data": df.head(3).to_dict('records') if len(df) > 0 else []
            }
            
        except Exception as e:
            logger.error(f"Error getting file info for {file_path}: {str(e)}")
            return {"error": str(e)}
