/**
 * Progress Bar Component
 * Handles progress display for domain processing
 */
export class ProgressBar {
    constructor(options = {}) {
        this.config = {
            animationDuration: 300,
            updateInterval: 100,
            showETA: true,
            showDetails: true,
            showSpeed: true,
            ...options
        };

        // State
        this.isVisible = false;
        this.currentProgress = 0;
        this.totalItems = 0;
        this.processedItems = 0;
        this.startTime = null;
        this.lastUpdateTime = null;
        this.progressHistory = [];

        // DOM elements
        this.elements = {};
        
        this.init();
    }

    /**
     * Initializes the component
     */
    init() {
        this.bindElements();
        this.hide();
    }

    /**
     * Binds DOM elements
     */
    bindElements() {
        this.elements = {
            section: document.getElementById('progress-section'),
            text: document.getElementById('progress-text'),
            percent: document.getElementById('progress-percent'),
            bar: document.getElementById('progress-bar'),
            details: document.getElementById('progress-details'),
            eta: document.getElementById('eta')
        };
    }

    /**
     * Shows the progress bar
     * @param {Object} options - Display options
     */
    show(options = {}) {
        const opts = {
            total: 0,
            current: 0,
            text: 'Processing...',
            ...options
        };

        this.isVisible = true;
        this.totalItems = opts.total;
        this.processedItems = opts.current;
        this.startTime = Date.now();
        this.lastUpdateTime = this.startTime;
        this.progressHistory = [];

        if (this.elements.section) {
            this.elements.section.classList.remove('hidden');
        }

        this.updateProgress({
            current: opts.current,
            total: opts.total,
            text: opts.text
        });
    }

    /**
     * Hides the progress bar
     */
    hide() {
        this.isVisible = false;
        
        if (this.elements.section) {
            this.elements.section.classList.add('hidden');
        }

        this.reset();
    }

    /**
     * Updates progress
     * @param {Object} progress - Progress data
     */
    updateProgress(progress = {}) {
        if (!this.isVisible) return;

        const now = Date.now();
        const {
            current = this.processedItems,
            total = this.totalItems,
            text = 'Processing...',
            details = null
        } = progress;

        // Update state
        this.processedItems = current;
        this.totalItems = total;
        this.lastUpdateTime = now;

        // Calculate percentage
        const percentage = total > 0 ? Math.min(Math.round((current / total) * 100), 100) : 0;
        this.currentProgress = percentage;

        // Record progress for speed calculation
        this.recordProgress(now, current);

        // Update UI elements
        this.updateProgressText(text);
        this.updateProgressPercentage(percentage);
        this.updateProgressBar(percentage);
        this.updateProgressDetails(current, total, details);
        
        if (this.config.showETA) {
            this.updateETA(current, total, now);
        }
    }

    /**
     * Updates progress text
     * @param {string} text - Progress text
     */
    updateProgressText(text) {
        if (this.elements.text) {
            this.elements.text.textContent = text;
        }
    }

    /**
     * Updates progress percentage
     * @param {number} percentage - Progress percentage
     */
    updateProgressPercentage(percentage) {
        if (this.elements.percent) {
            this.elements.percent.textContent = `${percentage}%`;
        }
    }

    /**
     * Updates progress bar visual
     * @param {number} percentage - Progress percentage
     */
    updateProgressBar(percentage) {
        if (this.elements.bar) {
            this.elements.bar.style.width = `${percentage}%`;
            
            // Add color transitions based on progress
            this.elements.bar.classList.remove('bg-red-600', 'bg-yellow-600', 'bg-green-600');
            
            if (percentage < 25) {
                this.elements.bar.classList.add('bg-red-600');
            } else if (percentage < 75) {
                this.elements.bar.classList.add('bg-yellow-600');
            } else {
                this.elements.bar.classList.add('bg-green-600');
            }
        }
    }

    /**
     * Updates progress details
     * @param {number} current - Current progress
     * @param {number} total - Total items
     * @param {string} details - Additional details
     */
    updateProgressDetails(current, total, details = null) {
        if (this.elements.details) {
            const defaultDetails = `${current}/${total} domain`;
            const displayDetails = details || defaultDetails;
            
            if (this.config.showSpeed) {
                const speed = this.calculateSpeed();
                if (speed > 0) {
                    this.elements.details.textContent = `${displayDetails} (${speed.toFixed(1)}/s)`;
                } else {
                    this.elements.details.textContent = displayDetails;
                }
            } else {
                this.elements.details.textContent = displayDetails;
            }
        }
    }

    /**
     * Updates ETA display
     * @param {number} current - Current progress
     * @param {number} total - Total items
     * @param {number} now - Current timestamp
     */
    updateETA(current, total, now) {
        if (!this.elements.eta || current === 0 || !this.startTime) {
            return;
        }

        const elapsed = now - this.startTime;
        const remaining = total - current;
        
        if (remaining <= 0) {
            this.elements.eta.textContent = 'Hoàn thành';
            return;
        }

        // Calculate ETA based on average speed
        const speed = this.calculateAverageSpeed();
        if (speed > 0) {
            const etaSeconds = remaining / speed;
            this.elements.eta.textContent = `Ước tính: ${this.formatDuration(etaSeconds * 1000)}`;
        } else {
            this.elements.eta.textContent = 'Ước tính: --';
        }
    }

    /**
     * Records progress for speed calculation
     * @param {number} timestamp - Timestamp
     * @param {number} processed - Items processed
     */
    recordProgress(timestamp, processed) {
        this.progressHistory.push({ timestamp, processed });
        
        // Keep only last 10 records for moving average
        if (this.progressHistory.length > 10) {
            this.progressHistory.shift();
        }
    }

    /**
     * Calculates current processing speed
     * @returns {number} Items per second
     */
    calculateSpeed() {
        if (this.progressHistory.length < 2) {
            return 0;
        }

        const recent = this.progressHistory.slice(-2);
        const timeDiff = (recent[1].timestamp - recent[0].timestamp) / 1000;
        const processDiff = recent[1].processed - recent[0].processed;

        return timeDiff > 0 ? processDiff / timeDiff : 0;
    }

    /**
     * Calculates average processing speed
     * @returns {number} Average items per second
     */
    calculateAverageSpeed() {
        if (this.progressHistory.length < 2 || !this.startTime) {
            return 0;
        }

        const elapsed = (this.lastUpdateTime - this.startTime) / 1000;
        return elapsed > 0 ? this.processedItems / elapsed : 0;
    }

    /**
     * Sets progress to indeterminate state
     * @param {string} text - Progress text
     */
    setIndeterminate(text = 'Processing...') {
        if (!this.isVisible) return;

        this.updateProgressText(text);
        
        if (this.elements.percent) {
            this.elements.percent.textContent = '';
        }

        if (this.elements.bar) {
            this.elements.bar.style.width = '100%';
            this.elements.bar.classList.add('animate-pulse');
        }

        if (this.elements.details) {
            this.elements.details.textContent = '';
        }

        if (this.elements.eta) {
            this.elements.eta.textContent = '';
        }
    }

    /**
     * Sets progress to error state
     * @param {string} errorMessage - Error message
     */
    setError(errorMessage = 'An error occurred') {
        if (!this.isVisible) return;

        this.updateProgressText(errorMessage);
        
        if (this.elements.bar) {
            this.elements.bar.style.width = '100%';
            this.elements.bar.classList.remove('bg-primary-600', 'bg-yellow-600', 'bg-green-600');
            this.elements.bar.classList.add('bg-red-600');
        }

        if (this.elements.percent) {
            this.elements.percent.textContent = 'Error';
            this.elements.percent.classList.add('text-red-600');
        }
    }

    /**
     * Sets progress to complete state
     * @param {string} message - Completion message
     */
    setComplete(message = 'Completed') {
        this.updateProgress({
            current: this.totalItems,
            total: this.totalItems,
            text: message
        });

        if (this.elements.bar) {
            this.elements.bar.classList.remove('bg-red-600', 'bg-yellow-600');
            this.elements.bar.classList.add('bg-green-600');
        }

        if (this.elements.eta) {
            this.elements.eta.textContent = 'Hoàn thành';
        }
    }

    /**
     * Sets progress to paused state
     * @param {string} message - Pause message
     */
    setPaused(message = 'Paused') {
        this.updateProgressText(message);
        
        if (this.elements.bar) {
            this.elements.bar.classList.remove('bg-primary-600', 'bg-red-600', 'bg-green-600');
            this.elements.bar.classList.add('bg-yellow-600');
        }

        if (this.elements.eta) {
            this.elements.eta.textContent = 'Đã tạm dừng';
        }
    }

    /**
     * Formats duration in human readable format
     * @param {number} milliseconds - Duration in milliseconds
     * @returns {string} Formatted duration
     */
    formatDuration(milliseconds) {
        const seconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);

        if (hours > 0) {
            return `${hours}h ${minutes % 60}m`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds % 60}s`;
        } else {
            return `${seconds}s`;
        }
    }

    /**
     * Gets current progress state
     * @returns {Object} Progress state
     */
    getState() {
        return {
            isVisible: this.isVisible,
            currentProgress: this.currentProgress,
            totalItems: this.totalItems,
            processedItems: this.processedItems,
            startTime: this.startTime,
            elapsed: this.startTime ? Date.now() - this.startTime : 0,
            speed: this.calculateSpeed(),
            averageSpeed: this.calculateAverageSpeed()
        };
    }

    /**
     * Resets progress state
     */
    reset() {
        this.currentProgress = 0;
        this.totalItems = 0;
        this.processedItems = 0;
        this.startTime = null;
        this.lastUpdateTime = null;
        this.progressHistory = [];

        // Reset UI elements
        if (this.elements.bar) {
            this.elements.bar.style.width = '0%';
            this.elements.bar.classList.remove('bg-red-600', 'bg-yellow-600', 'bg-green-600', 'animate-pulse');
            this.elements.bar.classList.add('bg-primary-600');
        }

        if (this.elements.percent) {
            this.elements.percent.textContent = '0%';
            this.elements.percent.classList.remove('text-red-600');
        }

        if (this.elements.text) {
            this.elements.text.textContent = '';
        }

        if (this.elements.details) {
            this.elements.details.textContent = '';
        }

        if (this.elements.eta) {
            this.elements.eta.textContent = '';
        }
    }

    /**
     * Animates progress bar to specific percentage
     * @param {number} targetPercentage - Target percentage
     * @param {number} duration - Animation duration
     */
    animateToPercentage(targetPercentage, duration = this.config.animationDuration) {
        if (!this.elements.bar) return;

        const startPercentage = this.currentProgress;
        const difference = targetPercentage - startPercentage;
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Easing function (ease-out)
            const eased = 1 - Math.pow(1 - progress, 3);
            const currentPercentage = startPercentage + (difference * eased);
            
            this.elements.bar.style.width = `${currentPercentage}%`;
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                this.currentProgress = targetPercentage;
            }
        };

        requestAnimationFrame(animate);
    }

    /**
     * Updates configuration
     * @param {Object} newConfig - New configuration
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
    }

    /**
     * Toggles visibility
     */
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }
}
