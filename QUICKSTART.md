# Quick Start Guide - Domain Availability Checker

## Khởi chạy nhanh trong 5 phút

### 1. Cài đặt Python dependencies

**Phương pháp 1 - Python Script (Khuyến nghị):**
```cmd
python quick-install.py
```

**Phương pháp 2 - Bat<PERSON>t (Windows):**
```cmd
install-manual.bat
```

**Phương pháp 3 - Manual từng package:**
```cmd
pip install fastapi==0.100.0 uvicorn==0.22.0 selenium==4.10.0
pip install pandas numpy webdriver-manager python-dotenv
pip install jinja2 aiofiles websockets httpx pydantic==1.10.12
```

**Phương pháp 4 - Requirements file:**
```cmd
pip install -r requirements-simple.txt
```

> **Lưu ý**: Nếu gặp lỗi Rust compiler với pydantic, hãy dùng phương pháp 1 hoặc 2

### 2. Khởi chạy ứng dụng

```bash
python run.py
```

### 3. <PERSON><PERSON><PERSON> cập ứng dụng

Mở trình duyệt và truy cập: **http://localhost:8000**

### 4. Test với domain mẫu

Copy và paste danh sách domain sau vào textarea:

```
example-test-domain-12345.com
another-test-domain-67890.net
sample-domain-testing-999.org
unique-test-domain-2024.info
demo-domain-checker-test.biz
```

### 5. Cấu hình Selenium (Tùy chọn)

**Để xem quá trình automation:**
- Bỏ tick "Chế độ headless"
- Bạn sẽ thấy Chrome browser mở và tự động thực hiện các thao tác

**Chế độ debug:**
- Tick "Chế độ debug" để có thêm log chi tiết
- Hữu ích khi troubleshooting

**Timeout:**
- 15s: Nhanh (có thể bị lỗi nếu mạng chậm)
- 30s: Mặc định (khuyến nghị)
- 60s+: Cho mạng chậm

### 6. Bắt đầu kiểm tra

1. Nhập tên công việc (tùy chọn): "Test Domain Check"
2. Cấu hình Selenium options nếu cần
3. Click nút "Kiểm tra Domain"
4. Theo dõi tiến độ real-time
5. Tải file CSV kết quả khi hoàn tất

## Cấu hình nhanh

### Chế độ debug (không headless)

Tạo file `.env`:

```env
HEADLESS_MODE=False
DEBUG=True
```

### Giảm timeout cho test nhanh

```env
SELENIUM_TIMEOUT=15
PAGE_LOAD_TIMEOUT=30
RESULT_CHECK_INTERVAL=1
```

## Troubleshooting nhanh

### Lỗi Chrome WebDriver

```bash
# Cài đặt Chrome browser trước
# WebDriver sẽ tự động download
```

### Lỗi permission

```bash
# Windows: Chạy terminal as Administrator
# Linux/Mac: chmod +x run.py
```

### Test API trực tiếp

```bash
curl -X POST "http://localhost:8000/api/check-domains" \
     -H "Content-Type: application/json" \
     -d '{"domains": ["test1.com", "test2.com"]}'
```

## Các endpoint hữu ích

- **Main App**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health
- **WebSocket Test**: ws://localhost:8000/ws/general

## Cấu trúc file quan trọng

```
app/
├── main.py              # Entry point
├── config.py            # Cấu hình
├── models/              # Pydantic models
├── routers/             # API routes
├── services/            # Business logic
├── templates/           # HTML templates
└── static/              # CSS/JS files

run.py                   # Script khởi chạy
requirements.txt         # Dependencies
.env.example            # Cấu hình mẫu
```

Chúc bạn sử dụng thành công! 🚀
