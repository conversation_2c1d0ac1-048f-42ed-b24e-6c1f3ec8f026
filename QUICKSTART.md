# Quick Start Guide - Domain Availability Checker

## Khởi chạy nhanh trong 5 phút

### 1. Cài đặt Python dependencies

```bash
pip install -r requirements.txt
```

### 2. Khởi chạy ứng dụng

```bash
python run.py
```

### 3. Truy cập ứng dụng

Mở trình duyệt và truy cập: **http://localhost:8000**

### 4. Test với domain mẫu

Copy và paste danh sách domain sau vào textarea:

```
example-test-domain-12345.com
another-test-domain-67890.net
sample-domain-testing-999.org
unique-test-domain-2024.info
demo-domain-checker-test.biz
```

### 5. Bắt đầu kiểm tra

1. Nhập tên công việc (tùy chọn): "Test Domain Check"
2. Click nút "Kiểm tra Domain"
3. <PERSON> dõi tiến độ real-time
4. Tải file CSV kết quả khi hoàn tất

## C<PERSON>u hình nhanh

### Chế độ debug (không headless)

Tạo file `.env`:

```env
HEADLESS_MODE=False
DEBUG=True
```

### Giảm timeout cho test nhanh

```env
SELENIUM_TIMEOUT=15
PAGE_LOAD_TIMEOUT=30
RESULT_CHECK_INTERVAL=1
```

## Troubleshooting nhanh

### Lỗi Chrome WebDriver

```bash
# Cài đặt Chrome browser trước
# WebDriver sẽ tự động download
```

### Lỗi permission

```bash
# Windows: Chạy terminal as Administrator
# Linux/Mac: chmod +x run.py
```

### Test API trực tiếp

```bash
curl -X POST "http://localhost:8000/api/check-domains" \
     -H "Content-Type: application/json" \
     -d '{"domains": ["test1.com", "test2.com"]}'
```

## Các endpoint hữu ích

- **Main App**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health
- **WebSocket Test**: ws://localhost:8000/ws/general

## Cấu trúc file quan trọng

```
app/
├── main.py              # Entry point
├── config.py            # Cấu hình
├── models/              # Pydantic models
├── routers/             # API routes
├── services/            # Business logic
├── templates/           # HTML templates
└── static/              # CSS/JS files

run.py                   # Script khởi chạy
requirements.txt         # Dependencies
.env.example            # Cấu hình mẫu
```

Chúc bạn sử dụng thành công! 🚀
