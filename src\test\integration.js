/**
 * Integration Test Suite
 * Tests the complete application integration
 */

// Test configuration
const TEST_CONFIG = {
    testDomains: [
        'example.com',
        'google.com',
        'github.com',
        'stackoverflow.com',
        'invalid-domain-test'
    ],
    maxTestTime: 30000, // 30 seconds
    enableRealAPI: false // Set to true for real API testing
};

class IntegrationTester {
    constructor() {
        this.results = [];
        this.errors = [];
        this.startTime = null;
        this.endTime = null;
    }

    /**
     * Runs all integration tests
     */
    async runAllTests() {
        console.log('🚀 Starting Wayback Machine Checker Integration Tests');
        this.startTime = Date.now();

        try {
            await this.testComponentInitialization();
            await this.testDomainValidation();
            await this.testFileUpload();
            await this.testRateLimiting();
            
            if (TEST_CONFIG.enableRealAPI) {
                await this.testWaybackAPI();
                await this.testFullProcessing();
            } else {
                await this.testMockProcessing();
            }
            
            await this.testExportFunctionality();
            await this.testStateManagement();
            await this.testErrorHandling();
            await this.testUIInteractions();

            this.endTime = Date.now();
            this.showResults();

        } catch (error) {
            console.error('❌ Test suite failed:', error);
            this.errors.push({
                test: 'Integration Test Suite',
                error: error.message,
                timestamp: Date.now()
            });
            this.showResults();
        }
    }

    /**
     * Tests component initialization
     */
    async testComponentInitialization() {
        console.log('🧪 Testing component initialization...');
        
        try {
            // Check if AppManager is properly initialized
            if (typeof window.app === 'undefined') {
                throw new Error('AppManager not initialized');
            }

            // Check components
            const requiredComponents = ['domainInput', 'progressBar', 'resultsTable', 'exportButtons'];
            for (const component of requiredComponents) {
                if (!window.app.components[component]) {
                    throw new Error(`${component} not initialized`);
                }
            }

            // Check services
            const requiredServices = ['rateLimiter', 'waybackAPI', 'dataProcessor'];
            for (const service of requiredServices) {
                if (!window.app[service]) {
                    throw new Error(`${service} not initialized`);
                }
            }

            this.addResult('Component Initialization', 'PASS', 'All components and services initialized');

        } catch (error) {
            this.addResult('Component Initialization', 'FAIL', error.message);
        }
    }

    /**
     * Tests domain validation
     */
    async testDomainValidation() {
        console.log('🧪 Testing domain validation...');
        
        try {
            const validator = window.app.domainValidator;
            
            // Test valid domains
            const validDomains = ['example.com', 'google.com', 'test.co.uk'];
            for (const domain of validDomains) {
                if (!validator.isValidDomain(domain)) {
                    throw new Error(`Valid domain rejected: ${domain}`);
                }
            }

            // Test invalid domains
            const invalidDomains = ['invalid', 'http://example.com', ''];
            for (const domain of invalidDomains) {
                if (validator.isValidDomain(domain)) {
                    throw new Error(`Invalid domain accepted: ${domain}`);
                }
            }

            // Test batch validation
            const batchResult = validator.validateBatch([...validDomains, ...invalidDomains]);
            if (batchResult.valid.length !== validDomains.length) {
                throw new Error('Batch validation failed');
            }

            this.addResult('Domain Validation', 'PASS', `${validDomains.length} valid, ${invalidDomains.length} invalid`);

        } catch (error) {
            this.addResult('Domain Validation', 'FAIL', error.message);
        }
    }

    /**
     * Tests file upload functionality
     */
    async testFileUpload() {
        console.log('🧪 Testing file upload...');
        
        try {
            // Create test CSV content
            const csvContent = 'domain\nexample.com\ngoogle.com\ntest.co.uk';
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const file = new File([blob], 'test-domains.csv', { type: 'text/csv' });

            // Test file parsing
            const domainInput = window.app.components.domainInput;
            const result = await domainInput.parseFile(file);
            
            if (!result.domains || result.domains.length !== 3) {
                throw new Error('File parsing failed');
            }

            if (!result.domains.includes('example.com')) {
                throw new Error('Domain extraction failed');
            }

            this.addResult('File Upload', 'PASS', `Parsed ${result.domains.length} domains from CSV`);

        } catch (error) {
            this.addResult('File Upload', 'FAIL', error.message);
        }
    }

    /**
     * Tests rate limiting
     */
    async testRateLimiting() {
        console.log('🧪 Testing rate limiting...');
        
        try {
            const rateLimiter = window.app.rateLimiter;
            const startTime = Date.now();
            
            // Test multiple requests
            await rateLimiter.waitForSlot();
            const firstDelay = Date.now() - startTime;
            
            await rateLimiter.waitForSlot();
            const secondDelay = Date.now() - startTime;
            
            // Should have proper delay between requests (around 1250ms for 0.8 req/sec)
            const expectedDelay = 1250; // 1000/0.8
            const tolerance = 200;
            
            if (secondDelay < expectedDelay - tolerance) {
                throw new Error(`Rate limiting too lenient: ${secondDelay}ms < ${expectedDelay}ms`);
            }

            this.addResult('Rate Limiting', 'PASS', `Proper delays: ${firstDelay}ms, ${secondDelay}ms`);

        } catch (error) {
            this.addResult('Rate Limiting', 'FAIL', error.message);
        }
    }

    /**
     * Tests Wayback Machine API
     */
    async testWaybackAPI() {
        console.log('🧪 Testing Wayback API...');
        
        try {
            const waybackAPI = window.app.waybackAPI;
            
            // Test with a known domain
            const snapshots = await waybackAPI.getSnapshots('example.com');
            
            if (!Array.isArray(snapshots)) {
                throw new Error('API response is not an array');
            }

            if (snapshots.length === 0) {
                throw new Error('No snapshots returned for example.com');
            }

            // Validate snapshot structure
            const firstSnapshot = snapshots[0];
            const requiredFields = ['timestamp', 'statuscode'];
            for (const field of requiredFields) {
                if (!(field in firstSnapshot)) {
                    throw new Error(`Missing field in snapshot: ${field}`);
                }
            }

            this.addResult('Wayback API', 'PASS', `Retrieved ${snapshots.length} snapshots`);

        } catch (error) {
            this.addResult('Wayback API', 'FAIL', error.message);
        }
    }

    /**
     * Tests mock processing (when real API is disabled)
     */
    async testMockProcessing() {
        console.log('🧪 Testing mock processing...');
        
        try {
            // Set test domains
            window.app.state.domains = TEST_CONFIG.testDomains.slice(0, 3);
            
            // Mock the processSingleDomain method
            const originalMethod = window.app.processSingleDomain;
            window.app.processSingleDomain = async (domain) => {
                // Simulate processing delay
                await new Promise(resolve => setTimeout(resolve, 100));
                
                // Return mock result
                return {
                    domain,
                    error: domain.includes('invalid') ? 'Mock error' : null,
                    snapshotCount: domain.includes('invalid') ? 0 : Math.floor(Math.random() * 100) + 10,
                    firstSnapshot: '2020-01-01',
                    lastSnapshot: '2023-12-31',
                    statusCodes: { '200': 50, '404': 5 },
                    totalSize: Math.floor(Math.random() * 1000000),
                    responseTime: Math.floor(Math.random() * 1000)
                };
            };

            // Run processing
            await window.app.startProcessing();
            
            // Wait for completion
            await this.waitForProcessingComplete();
            
            // Restore original method
            window.app.processSingleDomain = originalMethod;
            
            // Verify results
            const results = window.app.state.results;
            if (results.length !== 3) {
                throw new Error(`Expected 3 results, got ${results.length}`);
            }

            this.addResult('Mock Processing', 'PASS', `Processed ${results.length} domains`);

        } catch (error) {
            this.addResult('Mock Processing', 'FAIL', error.message);
        }
    }

    /**
     * Tests full processing with real API
     */
    async testFullProcessing() {
        console.log('🧪 Testing full processing...');
        
        try {
            // Use only 2 domains to avoid long test times
            window.app.state.domains = ['example.com', 'httpbin.org'];
            
            // Start processing
            await window.app.startProcessing();
            
            // Wait for completion
            await this.waitForProcessingComplete();
            
            // Verify results
            const results = window.app.state.results;
            if (results.length !== 2) {
                throw new Error(`Expected 2 results, got ${results.length}`);
            }

            // Check that at least one result is successful
            const successCount = results.filter(r => !r.error).length;
            if (successCount === 0) {
                throw new Error('No successful results');
            }

            this.addResult('Full Processing', 'PASS', `${successCount}/2 domains successful`);

        } catch (error) {
            this.addResult('Full Processing', 'FAIL', error.message);
        }
    }

    /**
     * Tests export functionality
     */
    async testExportFunctionality() {
        console.log('🧪 Testing export functionality...');
        
        try {
            const exportButtons = window.app.components.exportButtons;
            
            // Set test data
            const testData = [
                {
                    domain: 'example.com',
                    snapshotCount: 50,
                    firstSnapshot: '2020-01-01',
                    lastSnapshot: '2023-12-31',
                    statusCodes: { '200': 45, '404': 5 },
                    totalSize: 1000000
                }
            ];
            
            exportButtons.setData(testData);

            // Test CSV export
            const csvContent = exportButtons.formatDataForExport('csv');
            if (!csvContent.includes('example.com')) {
                throw new Error('CSV export failed');
            }

            // Test JSON export
            const jsonContent = exportButtons.formatDataForExport('json');
            const jsonData = JSON.parse(jsonContent);
            if (!jsonData.results || jsonData.results.length !== 1) {
                throw new Error('JSON export failed');
            }

            // Test TSV export
            const tsvContent = exportButtons.formatDataForExport('tsv');
            if (!tsvContent.includes('example.com')) {
                throw new Error('TSV export failed');
            }

            this.addResult('Export Functionality', 'PASS', 'CSV, JSON, TSV exports working');

        } catch (error) {
            this.addResult('Export Functionality', 'FAIL', error.message);
        }
    }

    /**
     * Tests state management
     */
    async testStateManagement() {
        console.log('🧪 Testing state management...');
        
        try {
            // Test save state
            window.app.state.domains = ['test1.com', 'test2.com'];
            window.app.saveState();
            
            // Test restore state
            const savedState = localStorage.getItem('wayback-checker-state');
            if (!savedState) {
                throw new Error('State not saved to localStorage');
            }

            const parsedState = JSON.parse(savedState);
            if (!parsedState.domains || parsedState.domains.length !== 2) {
                throw new Error('State save/restore failed');
            }

            this.addResult('State Management', 'PASS', 'Save/restore working');

        } catch (error) {
            this.addResult('State Management', 'FAIL', error.message);
        }
    }

    /**
     * Tests error handling
     */
    async testErrorHandling() {
        console.log('🧪 Testing error handling...');
        
        try {
            let errorCaught = false;
            
            // Test invalid domain processing
            try {
                await window.app.processSingleDomain('invalid-domain-for-testing');
            } catch (error) {
                errorCaught = true;
            }

            // Test rate limiter error handling
            const rateLimiter = window.app.rateLimiter;
            const originalWait = rateLimiter.waitForSlot;
            rateLimiter.waitForSlot = async () => {
                throw new Error('Test rate limiter error');
            };

            try {
                await window.app.waybackAPI.getSnapshots('test.com');
            } catch (error) {
                errorCaught = true;
            }

            // Restore original method
            rateLimiter.waitForSlot = originalWait;

            if (!errorCaught) {
                throw new Error('No errors were caught during error handling test');
            }

            this.addResult('Error Handling', 'PASS', 'Errors properly caught and handled');

        } catch (error) {
            this.addResult('Error Handling', 'FAIL', error.message);
        }
    }

    /**
     * Tests UI interactions
     */
    async testUIInteractions() {
        console.log('🧪 Testing UI interactions...');
        
        try {
            // Test domain input
            const domainTextarea = document.getElementById('domain-input');
            if (domainTextarea) {
                domainTextarea.value = 'test.com\nexample.com';
                domainTextarea.dispatchEvent(new Event('input'));
                
                // Give time for processing
                await new Promise(resolve => setTimeout(resolve, 100));
                
                if (window.app.state.domains.length < 2) {
                    throw new Error('Domain input not working');
                }
            }

            // Test start button
            const startBtn = document.getElementById('start-btn');
            if (startBtn && !startBtn.disabled) {
                // Button should be enabled with domains
            } else {
                throw new Error('Start button state incorrect');
            }

            // Test progress bar visibility
            const progressContainer = document.getElementById('progress-container');
            if (progressContainer && progressContainer.style.display === 'none') {
                // Progress should be hidden when not processing
            }

            this.addResult('UI Interactions', 'PASS', 'Basic UI interactions working');

        } catch (error) {
            this.addResult('UI Interactions', 'FAIL', error.message);
        }
    }

    /**
     * Waits for processing to complete
     * @param {number} maxWait - Maximum wait time in ms
     */
    async waitForProcessingComplete(maxWait = TEST_CONFIG.maxTestTime) {
        const startTime = Date.now();
        
        while (window.app.state.isProcessing) {
            if (Date.now() - startTime > maxWait) {
                throw new Error('Processing timeout');
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }
    }

    /**
     * Adds a test result
     * @param {string} testName - Name of the test
     * @param {string} status - Test status (PASS/FAIL)
     * @param {string} details - Test details
     */
    addResult(testName, status, details) {
        this.results.push({
            testName,
            status,
            details,
            timestamp: Date.now()
        });

        const emoji = status === 'PASS' ? '✅' : '❌';
        console.log(`${emoji} ${testName}: ${details}`);
    }

    /**
     * Shows test results
     */
    showResults() {
        const duration = this.endTime ? this.endTime - this.startTime : Date.now() - this.startTime;
        const passCount = this.results.filter(r => r.status === 'PASS').length;
        const failCount = this.results.filter(r => r.status === 'FAIL').length;
        
        console.log('\n📊 Integration Test Results');
        console.log('='.repeat(50));
        console.log(`Total Tests: ${this.results.length}`);
        console.log(`✅ Passed: ${passCount}`);
        console.log(`❌ Failed: ${failCount}`);
        console.log(`⏱️ Duration: ${duration}ms`);
        console.log('='.repeat(50));

        if (failCount > 0) {
            console.log('\n❌ Failed Tests:');
            this.results.filter(r => r.status === 'FAIL').forEach(result => {
                console.log(`  - ${result.testName}: ${result.details}`);
            });
        }

        if (this.errors.length > 0) {
            console.log('\n🔥 Errors:');
            this.errors.forEach(error => {
                console.log(`  - ${error.test}: ${error.error}`);
            });
        }

        // Show summary in UI
        this.showResultsInUI(passCount, failCount, duration);
    }

    /**
     * Shows test results in UI
     * @param {number} passCount - Number of passed tests
     * @param {number} failCount - Number of failed tests
     * @param {number} duration - Test duration
     */
    showResultsInUI(passCount, failCount, duration) {
        const message = `Integration Tests: ${passCount} passed, ${failCount} failed (${duration}ms)`;
        const type = failCount > 0 ? 'warning' : 'success';
        
        if (window.app && window.app.showToast) {
            window.app.showToast(message, type);
        } else {
            alert(message);
        }
    }
}

// Auto-run tests when enabled
if (window.location.search.includes('test=true')) {
    // Wait for app to be initialized
    setTimeout(() => {
        const tester = new IntegrationTester();
        tester.runAllTests();
    }, 2000);
}

// Export for manual testing
window.IntegrationTester = IntegrationTester;
