#!/bin/bash

echo "========================================"
echo "Domain Availability Checker - Installer"
echo "========================================"
echo

echo "[1/4] Updating pip..."
python3 -m pip install --upgrade pip

echo
echo "[2/4] Installing dependencies..."
pip3 install -r requirements-minimal.txt

echo
echo "[3/4] Creating directories..."
mkdir -p uploads downloads temp logs

echo
echo "[4/4] Creating environment file..."
if [ ! -f ".env" ]; then
    cp .env.example .env
    echo "Environment file created from template"
else
    echo "Environment file already exists"
fi

echo
echo "========================================"
echo "Installation completed successfully!"
echo "========================================"
echo
echo "To start the application:"
echo "  python3 run.py"
echo
echo "Then open your browser and go to:"
echo "  http://localhost:8000"
echo
