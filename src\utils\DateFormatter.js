/**
 * Date Formatter Utility
 * Handles date formatting for Wayback Machine timestamps
 */
export class DateFormatter {
    constructor() {
        // Common date format patterns
        this.formats = {
            'dd/mm/yyyy': 'DD/MM/YYYY',
            'mm/dd/yyyy': 'MM/DD/YYYY',
            'yyyy-mm-dd': 'YYYY-MM-DD',
            'dd-mm-yyyy': 'DD-MM-YYYY',
            'dd.mm.yyyy': 'DD.MM.YYYY',
            'iso': 'ISO 8601',
            'timestamp': 'Unix Timestamp',
            'wayback': 'Wayback Format (YYYYMMDDHHMMSS)'
        };

        // Default format for display
        this.defaultFormat = 'dd/mm/yyyy';
        
        // Month names for different locales
        this.monthNames = {
            en: ['January', 'February', 'March', 'April', 'May', 'June',
                 'July', 'August', 'September', 'October', 'November', 'December'],
            vi: ['Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
                 'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12']
        };
    }

    /**
     * Formats a Wayback Machine timestamp to readable date
     * @param {string} timestamp - Wayback timestamp (YYYYMMDDHHMMSS)
     * @param {string} format - Output format
     * @param {Object} options - Formatting options
     * @returns {string} Formatted date string
     */
    formatWaybackTimestamp(timestamp, format = null, options = {}) {
        const opts = {
            locale: 'vi',
            includeTime: false,
            fallback: 'N/A',
            validate: true,
            ...options
        };

        if (!timestamp || typeof timestamp !== 'string') {
            return opts.fallback;
        }

        // Clean timestamp (remove any non-digit characters)
        const cleanTimestamp = timestamp.replace(/\D/g, '');
        
        // Validate timestamp length
        if (opts.validate && cleanTimestamp.length < 8) {
            return opts.fallback;
        }

        try {
            // Extract date components
            const year = parseInt(cleanTimestamp.substring(0, 4));
            const month = parseInt(cleanTimestamp.substring(4, 6));
            const day = parseInt(cleanTimestamp.substring(6, 8));
            
            // Validate date components
            if (opts.validate) {
                if (year < 1900 || year > 2100 || month < 1 || month > 12 || day < 1 || day > 31) {
                    return opts.fallback;
                }
            }

            // Create date object
            const date = new Date(year, month - 1, day);
            
            // Validate date object
            if (opts.validate && isNaN(date.getTime())) {
                return opts.fallback;
            }

            // Extract time components if available and requested
            let time = '';
            if (opts.includeTime && cleanTimestamp.length >= 14) {
                const hour = cleanTimestamp.substring(8, 10);
                const minute = cleanTimestamp.substring(10, 12);
                const second = cleanTimestamp.substring(12, 14);
                time = ` ${hour}:${minute}:${second}`;
            }

            // Apply formatting
            const outputFormat = format || this.defaultFormat;
            const formattedDate = this.formatDate(date, outputFormat, opts);
            
            return formattedDate + time;
            
        } catch (error) {
            console.error('Error formatting timestamp:', error);
            return opts.fallback;
        }
    }

    /**
     * Formats a Date object to specified format
     * @param {Date} date - Date object to format
     * @param {string} format - Output format
     * @param {Object} options - Formatting options
     * @returns {string} Formatted date string
     */
    formatDate(date, format = 'dd/mm/yyyy', options = {}) {
        const opts = {
            locale: 'vi',
            padZero: true,
            ...options
        };

        if (!(date instanceof Date) || isNaN(date.getTime())) {
            return opts.fallback || 'Invalid Date';
        }

        const day = date.getDate();
        const month = date.getMonth() + 1;
        const year = date.getFullYear();

        const pad = (num) => opts.padZero ? num.toString().padStart(2, '0') : num.toString();

        switch (format.toLowerCase()) {
            case 'dd/mm/yyyy':
                return `${pad(day)}/${pad(month)}/${year}`;
            
            case 'mm/dd/yyyy':
                return `${pad(month)}/${pad(day)}/${year}`;
            
            case 'yyyy-mm-dd':
                return `${year}-${pad(month)}-${pad(day)}`;
            
            case 'dd-mm-yyyy':
                return `${pad(day)}-${pad(month)}-${year}`;
            
            case 'dd.mm.yyyy':
                return `${pad(day)}.${pad(month)}.${year}`;
            
            case 'iso':
                return date.toISOString().split('T')[0];
            
            case 'timestamp':
                return Math.floor(date.getTime() / 1000).toString();
            
            case 'wayback':
                return date.getFullYear().toString() +
                       pad(month) +
                       pad(day) +
                       '000000'; // Default time to 00:00:00
            
            case 'long':
                const monthName = this.monthNames[opts.locale] || this.monthNames.en;
                return `${day} ${monthName[month - 1]} ${year}`;
            
            case 'short':
                const shortMonth = this.monthNames[opts.locale] || this.monthNames.en;
                return `${day} ${shortMonth[month - 1].substring(0, 3)} ${year}`;
            
            default:
                // Custom format using placeholders
                return format
                    .replace(/YYYY/g, year)
                    .replace(/MM/g, pad(month))
                    .replace(/DD/g, pad(day))
                    .replace(/M/g, month)
                    .replace(/D/g, day);
        }
    }

    /**
     * Parses various date formats and returns Date object
     * @param {string} dateString - Date string to parse
     * @param {string} inputFormat - Expected input format
     * @returns {Date|null} Parsed Date object or null if invalid
     */
    parseDate(dateString, inputFormat = 'auto') {
        if (!dateString || typeof dateString !== 'string') {
            return null;
        }

        const cleanDate = dateString.trim();

        try {
            // Auto-detect format
            if (inputFormat === 'auto') {
                return this.autoParseDate(cleanDate);
            }

            // Parse specific format
            switch (inputFormat.toLowerCase()) {
                case 'wayback':
                    return this.parseWaybackTimestamp(cleanDate);
                
                case 'dd/mm/yyyy':
                    return this.parseDDMMYYYY(cleanDate);
                
                case 'mm/dd/yyyy':
                    return this.parseMMDDYYYY(cleanDate);
                
                case 'yyyy-mm-dd':
                    return this.parseYYYYMMDD(cleanDate);
                
                case 'iso':
                    return new Date(cleanDate);
                
                case 'timestamp':
                    return new Date(parseInt(cleanDate) * 1000);
                
                default:
                    return new Date(cleanDate);
            }
        } catch (error) {
            console.error('Error parsing date:', error);
            return null;
        }
    }

    /**
     * Auto-detects date format and parses
     * @param {string} dateString - Date string to parse
     * @returns {Date|null} Parsed Date object or null if invalid
     */
    autoParseDate(dateString) {
        const patterns = [
            // Wayback timestamp
            { regex: /^\d{14}$/, parser: this.parseWaybackTimestamp },
            { regex: /^\d{8}$/, parser: (str) => this.parseWaybackTimestamp(str + '000000') },
            
            // DD/MM/YYYY variants
            { regex: /^\d{1,2}\/\d{1,2}\/\d{4}$/, parser: this.parseDDMMYYYY },
            { regex: /^\d{1,2}-\d{1,2}-\d{4}$/, parser: (str) => this.parseDDMMYYYY(str.replace(/-/g, '/')) },
            { regex: /^\d{1,2}\.\d{1,2}\.\d{4}$/, parser: (str) => this.parseDDMMYYYY(str.replace(/\./g, '/')) },
            
            // YYYY-MM-DD variants
            { regex: /^\d{4}-\d{1,2}-\d{1,2}$/, parser: this.parseYYYYMMDD },
            { regex: /^\d{4}\/\d{1,2}\/\d{1,2}$/, parser: (str) => this.parseYYYYMMDD(str.replace(/\//g, '-')) },
            
            // Unix timestamp
            { regex: /^\d{10}$/, parser: (str) => new Date(parseInt(str) * 1000) },
            { regex: /^\d{13}$/, parser: (str) => new Date(parseInt(str)) }
        ];

        for (const pattern of patterns) {
            if (pattern.regex.test(dateString)) {
                try {
                    const result = pattern.parser.call(this, dateString);
                    if (result && !isNaN(result.getTime())) {
                        return result;
                    }
                } catch (error) {
                    continue;
                }
            }
        }

        // Fallback to native Date parsing
        try {
            const date = new Date(dateString);
            return !isNaN(date.getTime()) ? date : null;
        } catch {
            return null;
        }
    }

    /**
     * Parses Wayback timestamp format (YYYYMMDDHHMMSS)
     * @param {string} timestamp - Wayback timestamp
     * @returns {Date} Parsed Date object
     */
    parseWaybackTimestamp(timestamp) {
        const clean = timestamp.replace(/\D/g, '');
        if (clean.length < 8) {
            throw new Error('Invalid Wayback timestamp');
        }

        const year = parseInt(clean.substring(0, 4));
        const month = parseInt(clean.substring(4, 6)) - 1; // Month is 0-indexed
        const day = parseInt(clean.substring(6, 8));
        
        let hour = 0, minute = 0, second = 0;
        if (clean.length >= 14) {
            hour = parseInt(clean.substring(8, 10));
            minute = parseInt(clean.substring(10, 12));
            second = parseInt(clean.substring(12, 14));
        }

        return new Date(year, month, day, hour, minute, second);
    }

    /**
     * Parses DD/MM/YYYY format
     * @param {string} dateString - Date string in DD/MM/YYYY format
     * @returns {Date} Parsed Date object
     */
    parseDDMMYYYY(dateString) {
        const parts = dateString.split(/[\/\-\.]/);
        if (parts.length !== 3) {
            throw new Error('Invalid DD/MM/YYYY format');
        }

        const day = parseInt(parts[0]);
        const month = parseInt(parts[1]) - 1;
        const year = parseInt(parts[2]);

        return new Date(year, month, day);
    }

    /**
     * Parses MM/DD/YYYY format
     * @param {string} dateString - Date string in MM/DD/YYYY format
     * @returns {Date} Parsed Date object
     */
    parseMMDDYYYY(dateString) {
        const parts = dateString.split(/[\/\-\.]/);
        if (parts.length !== 3) {
            throw new Error('Invalid MM/DD/YYYY format');
        }

        const month = parseInt(parts[0]) - 1;
        const day = parseInt(parts[1]);
        const year = parseInt(parts[2]);

        return new Date(year, month, day);
    }

    /**
     * Parses YYYY-MM-DD format
     * @param {string} dateString - Date string in YYYY-MM-DD format
     * @returns {Date} Parsed Date object
     */
    parseYYYYMMDD(dateString) {
        const parts = dateString.split(/[\/\-\.]/);
        if (parts.length !== 3) {
            throw new Error('Invalid YYYY-MM-DD format');
        }

        const year = parseInt(parts[0]);
        const month = parseInt(parts[1]) - 1;
        const day = parseInt(parts[2]);

        return new Date(year, month, day);
    }

    /**
     * Calculates date range for display
     * @param {Array<string>} timestamps - Array of Wayback timestamps
     * @param {Object} options - Calculation options
     * @returns {Object} Date range information
     */
    calculateDateRange(timestamps, options = {}) {
        const opts = {
            format: 'dd/mm/yyyy',
            sort: true,
            validate: true,
            ...options
        };

        if (!Array.isArray(timestamps) || timestamps.length === 0) {
            return {
                count: 0,
                firstDate: null,
                lastDate: null,
                range: 'N/A',
                span: null
            };
        }

        // Parse and validate timestamps
        const validDates = timestamps
            .map(ts => this.parseDate(ts, 'wayback'))
            .filter(date => date && !isNaN(date.getTime()));

        if (validDates.length === 0) {
            return {
                count: 0,
                firstDate: null,
                lastDate: null,
                range: 'N/A',
                span: null
            };
        }

        // Sort dates if requested
        if (opts.sort) {
            validDates.sort((a, b) => a.getTime() - b.getTime());
        }

        const firstDate = validDates[0];
        const lastDate = validDates[validDates.length - 1];

        // Format dates
        const firstFormatted = this.formatDate(firstDate, opts.format);
        const lastFormatted = this.formatDate(lastDate, opts.format);

        // Calculate span
        const spanDays = Math.ceil((lastDate.getTime() - firstDate.getTime()) / (1000 * 60 * 60 * 24));
        
        return {
            count: validDates.length,
            firstDate: firstDate,
            lastDate: lastDate,
            range: firstFormatted === lastFormatted ? firstFormatted : `${firstFormatted} - ${lastFormatted}`,
            span: {
                days: spanDays,
                months: Math.ceil(spanDays / 30),
                years: Math.ceil(spanDays / 365)
            }
        };
    }

    /**
     * Validates timestamp format
     * @param {string} timestamp - Timestamp to validate
     * @param {string} format - Expected format
     * @returns {boolean} True if valid
     */
    isValidTimestamp(timestamp, format = 'wayback') {
        try {
            const parsed = this.parseDate(timestamp, format);
            return parsed !== null && !isNaN(parsed.getTime());
        } catch {
            return false;
        }
    }

    /**
     * Gets relative time description
     * @param {Date|string} date - Date to compare
     * @param {Date} baseDate - Base date for comparison (default: now)
     * @param {Object} options - Formatting options
     * @returns {string} Relative time description
     */
    getRelativeTime(date, baseDate = new Date(), options = {}) {
        const opts = {
            locale: 'vi',
            precision: 'auto',
            ...options
        };

        const targetDate = date instanceof Date ? date : this.parseDate(date);
        if (!targetDate) {
            return 'Invalid date';
        }

        const diffMs = baseDate.getTime() - targetDate.getTime();
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        const diffYears = Math.floor(diffDays / 365);

        const labels = opts.locale === 'vi' ? {
            future: 'trong tương lai',
            today: 'hôm nay',
            yesterday: 'hôm qua',
            daysAgo: 'ngày trước',
            monthsAgo: 'tháng trước',
            yearsAgo: 'năm trước'
        } : {
            future: 'in the future',
            today: 'today',
            yesterday: 'yesterday',
            daysAgo: 'days ago',
            monthsAgo: 'months ago',
            yearsAgo: 'years ago'
        };

        if (diffMs < 0) {
            return labels.future;
        } else if (diffDays === 0) {
            return labels.today;
        } else if (diffDays === 1) {
            return labels.yesterday;
        } else if (diffDays < 30) {
            return `${diffDays} ${labels.daysAgo}`;
        } else if (diffDays < 365) {
            const months = Math.floor(diffDays / 30);
            return `${months} ${labels.monthsAgo}`;
        } else {
            return `${diffYears} ${labels.yearsAgo}`;
        }
    }

    /**
     * Formats duration between two dates
     * @param {Date|string} startDate - Start date
     * @param {Date|string} endDate - End date
     * @param {Object} options - Formatting options
     * @returns {string} Formatted duration
     */
    formatDuration(startDate, endDate, options = {}) {
        const opts = {
            locale: 'vi',
            units: 'auto', // auto, days, months, years
            ...options
        };

        const start = startDate instanceof Date ? startDate : this.parseDate(startDate);
        const end = endDate instanceof Date ? endDate : this.parseDate(endDate);

        if (!start || !end) {
            return 'Invalid dates';
        }

        const diffMs = end.getTime() - start.getTime();
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        const diffMonths = Math.floor(diffDays / 30);
        const diffYears = Math.floor(diffDays / 365);

        const labels = opts.locale === 'vi' ? {
            day: 'ngày',
            days: 'ngày',
            month: 'tháng',
            months: 'tháng',
            year: 'năm',
            years: 'năm'
        } : {
            day: 'day',
            days: 'days',
            month: 'month',
            months: 'months',
            year: 'year',
            years: 'years'
        };

        if (opts.units === 'days' || (opts.units === 'auto' && diffDays < 60)) {
            return `${diffDays} ${diffDays === 1 ? labels.day : labels.days}`;
        } else if (opts.units === 'months' || (opts.units === 'auto' && diffDays < 730)) {
            return `${diffMonths} ${diffMonths === 1 ? labels.month : labels.months}`;
        } else {
            return `${diffYears} ${diffYears === 1 ? labels.year : labels.years}`;
        }
    }
}
