/**
 * Wayback Machine API Service
 * Handles all interactions with the Wayback Machine CDX API
 */
export class WaybackAPI {
    constructor(options = {}) {
        this.config = {
            baseUrl: 'http://web.archive.org/cdx/search/cdx',
            timeout: 30000,
            maxRetries: 3,
            userAgent: 'Wayback Domain Checker/1.0',
            rateLimit: {
                requestsPerSecond: 0.8,
                minDelayMs: 1250
            },
            ...options
        };

        // Request tracking
        this.requestCount = 0;
        this.lastRequestTime = 0;
        this.totalBytes = 0;
        this.errors = [];
        
        // Cache for session
        this.cache = new Map();
        this.cacheEnabled = options.enableCache !== false;
        this.cacheTTL = options.cacheTTL || 300000; // 5 minutes
    }

    /**
     * Searches for snapshots of a domain
     * @param {string} domain - Domain to search
     * @param {Object} options - Search options
     * @returns {Promise<Object>} Search results
     */
    async searchDomain(domain, options = {}) {
        const opts = {
            output: 'json',
            fl: 'timestamp,original,statuscode,digest,length',
            collapse: 'digest',
            fastLatest: false,
            ...options
        };

        // Validate domain
        if (!domain || typeof domain !== 'string') {
            throw new Error('Domain is required and must be a string');
        }

        const normalizedDomain = domain.trim().toLowerCase();
        
        // Check cache first
        if (this.cacheEnabled) {
            const cached = this.getFromCache(normalizedDomain, opts);
            if (cached) {
                return cached;
            }
        }

        // Build URL
        const url = this.buildSearchUrl(normalizedDomain, opts);
        
        try {
            // Apply rate limiting
            await this.applyRateLimit();
            
            // Make request
            const result = await this.makeRequest(url, opts);
            
            // Cache result
            if (this.cacheEnabled && result.success) {
                this.saveToCache(normalizedDomain, opts, result);
            }
            
            return result;
            
        } catch (error) {
            this.handleError(error, domain);
            throw error;
        }
    }

    /**
     * Builds search URL with parameters
     * @param {string} domain - Domain to search
     * @param {Object} options - Search options
     * @returns {string} Complete URL
     */
    buildSearchUrl(domain, options) {
        const params = new URLSearchParams();
        
        // Required parameters
        params.set('url', domain);
        params.set('output', options.output || 'json');
        
        // Optional parameters
        if (options.fl) {
            params.set('fl', options.fl);
        }
        
        if (options.collapse) {
            params.set('collapse', options.collapse);
        }
        
        if (options.from) {
            params.set('from', options.from);
        }
        
        if (options.to) {
            params.set('to', options.to);
        }
        
        if (options.matchType) {
            params.set('matchType', options.matchType);
        }
        
        if (options.limit) {
            params.set('limit', options.limit);
        }
        
        if (options.fastLatest) {
            params.set('fastLatest', 'true');
        }
        
        return `${this.config.baseUrl}?${params.toString()}`;
    }

    /**
     * Makes HTTP request to Wayback API
     * @param {string} url - Request URL
     * @param {Object} options - Request options
     * @returns {Promise<Object>} Response data
     */
    async makeRequest(url, options = {}) {
        const requestId = this.requestCount++;
        const startTime = Date.now();
        
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);
            
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'User-Agent': this.config.userAgent,
                    'Accept': 'application/json, text/plain, */*'
                },
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            
            // Update statistics
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            this.lastRequestTime = endTime;
            
            // Handle HTTP errors
            if (!response.ok) {
                const error = new Error(`HTTP ${response.status}: ${response.statusText}`);
                error.status = response.status;
                error.statusText = response.statusText;
                error.url = url;
                error.responseTime = responseTime;
                throw error;
            }
            
            // Get response data
            const text = await response.text();
            const contentLength = text.length;
            this.totalBytes += contentLength;
            
            // Parse response
            let data;
            try {
                data = JSON.parse(text);
            } catch (parseError) {
                // If JSON parsing fails, treat as empty result
                data = [];
            }
            
            return {
                success: true,
                data,
                metadata: {
                    requestId,
                    url,
                    responseTime,
                    contentLength,
                    status: response.status,
                    headers: Object.fromEntries(response.headers.entries())
                }
            };
            
        } catch (error) {
            const endTime = Date.now();
            const responseTime = endTime - startTime;
            
            // Enhance error with metadata
            error.requestId = requestId;
            error.url = url;
            error.responseTime = responseTime;
            
            // Handle specific error types
            if (error.name === 'AbortError') {
                error.message = `Request timeout after ${this.config.timeout}ms`;
                error.code = 'TIMEOUT';
            } else if (error.code === 'NETWORK_ERROR') {
                error.message = 'Network connection failed';
            }
            
            return {
                success: false,
                error: {
                    message: error.message,
                    code: error.code || error.name,
                    status: error.status,
                    responseTime,
                    url
                }
            };
        }
    }

    /**
     * Applies rate limiting delay
     * @returns {Promise<void>}
     */
    async applyRateLimit() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        const minDelay = this.config.rateLimit.minDelayMs;
        
        if (timeSinceLastRequest < minDelay) {
            const delay = minDelay - timeSinceLastRequest;
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

    /**
     * Processes raw CDX data into structured format
     * @param {Array} cdxData - Raw CDX response data
     * @param {string} domain - Original domain
     * @returns {Object} Processed snapshot data
     */
    processSnapshots(cdxData, domain) {
        if (!Array.isArray(cdxData) || cdxData.length === 0) {
            return {
                domain,
                snapshotCount: 0,
                snapshots: [],
                dateRange: null,
                statusCodes: {},
                firstSnapshot: null,
                lastSnapshot: null,
                totalSize: 0
            };
        }

        // Skip header row if present
        const snapshots = cdxData.length > 0 && Array.isArray(cdxData[0]) && 
                         cdxData[0].length > 0 && typeof cdxData[0][0] === 'string' && 
                         cdxData[0][0] === 'timestamp' ? 
                         cdxData.slice(1) : cdxData;

        const processedSnapshots = [];
        const statusCodes = {};
        const timestamps = [];
        let totalSize = 0;

        for (const snapshot of snapshots) {
            if (!Array.isArray(snapshot) || snapshot.length < 3) {
                continue;
            }

            const [timestamp, original, statuscode, digest, length] = snapshot;
            
            if (!timestamp) continue;

            const processed = {
                timestamp,
                original: original || domain,
                statusCode: statuscode || 'unknown',
                digest: digest || '',
                length: length ? parseInt(length) : 0,
                date: this.parseTimestamp(timestamp)
            };

            processedSnapshots.push(processed);
            timestamps.push(timestamp);
            
            // Count status codes
            statusCodes[processed.statusCode] = (statusCodes[processed.statusCode] || 0) + 1;
            
            // Sum total size
            if (processed.length) {
                totalSize += processed.length;
            }
        }

        // Sort timestamps
        timestamps.sort();

        return {
            domain,
            snapshotCount: processedSnapshots.length,
            snapshots: processedSnapshots,
            dateRange: timestamps.length > 0 ? {
                first: timestamps[0],
                last: timestamps[timestamps.length - 1]
            } : null,
            statusCodes,
            firstSnapshot: timestamps.length > 0 ? this.formatTimestamp(timestamps[0]) : null,
            lastSnapshot: timestamps.length > 0 ? this.formatTimestamp(timestamps[timestamps.length - 1]) : null,
            totalSize
        };
    }

    /**
     * Parses Wayback timestamp to Date object
     * @param {string} timestamp - Wayback timestamp (YYYYMMDDHHMMSS)
     * @returns {Date|null} Parsed date
     */
    parseTimestamp(timestamp) {
        if (!timestamp || timestamp.length < 8) {
            return null;
        }

        try {
            const clean = timestamp.toString().padEnd(14, '0');
            const year = parseInt(clean.substring(0, 4));
            const month = parseInt(clean.substring(4, 6));
            const day = parseInt(clean.substring(6, 8));
            const hour = parseInt(clean.substring(8, 10)) || 0;
            const minute = parseInt(clean.substring(10, 12)) || 0;
            const second = parseInt(clean.substring(12, 14)) || 0;

            return new Date(year, month - 1, day, hour, minute, second);
        } catch (error) {
            return null;
        }
    }

    /**
     * Formats timestamp for display
     * @param {string} timestamp - Wayback timestamp
     * @param {string} format - Output format
     * @returns {string} Formatted date
     */
    formatTimestamp(timestamp, format = 'dd/mm/yyyy') {
        const date = this.parseTimestamp(timestamp);
        if (!date) return '';

        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();

        switch (format) {
            case 'dd/mm/yyyy':
                return `${day}/${month}/${year}`;
            case 'mm/dd/yyyy':
                return `${month}/${day}/${year}`;
            case 'yyyy-mm-dd':
                return `${year}-${month}-${day}`;
            default:
                return `${day}/${month}/${year}`;
        }
    }

    /**
     * Gets cache key for request
     * @param {string} domain - Domain
     * @param {Object} options - Request options
     * @returns {string} Cache key
     */
    getCacheKey(domain, options) {
        const key = {
            domain: domain.toLowerCase(),
            output: options.output,
            fl: options.fl,
            collapse: options.collapse,
            from: options.from,
            to: options.to
        };
        return JSON.stringify(key);
    }

    /**
     * Gets data from cache
     * @param {string} domain - Domain
     * @param {Object} options - Request options
     * @returns {Object|null} Cached data or null
     */
    getFromCache(domain, options) {
        const key = this.getCacheKey(domain, options);
        const cached = this.cache.get(key);
        
        if (cached && Date.now() - cached.timestamp < this.cacheTTL) {
            return {
                ...cached.data,
                fromCache: true
            };
        }
        
        // Remove expired cache
        if (cached) {
            this.cache.delete(key);
        }
        
        return null;
    }

    /**
     * Saves data to cache
     * @param {string} domain - Domain
     * @param {Object} options - Request options
     * @param {Object} data - Data to cache
     */
    saveToCache(domain, options, data) {
        const key = this.getCacheKey(domain, options);
        this.cache.set(key, {
            data: { ...data, fromCache: false },
            timestamp: Date.now()
        });
        
        // Limit cache size
        if (this.cache.size > 1000) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
    }

    /**
     * Handles and logs errors
     * @param {Error} error - Error to handle
     * @param {string} domain - Domain that caused error
     */
    handleError(error, domain) {
        const errorInfo = {
            timestamp: new Date().toISOString(),
            domain,
            message: error.message,
            code: error.code || error.name,
            status: error.status,
            url: error.url,
            responseTime: error.responseTime
        };

        this.errors.push(errorInfo);
        
        // Keep only last 100 errors
        if (this.errors.length > 100) {
            this.errors.shift();
        }

        console.error('Wayback API Error:', errorInfo);
    }

    /**
     * Gets API statistics
     * @returns {Object} Statistics
     */
    getStats() {
        return {
            requestCount: this.requestCount,
            totalBytes: this.totalBytes,
            cacheSize: this.cache.size,
            errorCount: this.errors.length,
            lastRequestTime: this.lastRequestTime,
            averageResponseTime: this.calculateAverageResponseTime(),
            successRate: this.calculateSuccessRate()
        };
    }

    /**
     * Calculates average response time
     * @returns {number} Average response time in ms
     */
    calculateAverageResponseTime() {
        if (this.errors.length === 0) return 0;
        
        const totalTime = this.errors.reduce((sum, error) => 
            sum + (error.responseTime || 0), 0);
        
        return Math.round(totalTime / this.errors.length);
    }

    /**
     * Calculates success rate
     * @returns {number} Success rate percentage
     */
    calculateSuccessRate() {
        if (this.requestCount === 0) return 100;
        
        const successCount = this.requestCount - this.errors.length;
        return Math.round((successCount / this.requestCount) * 100);
    }

    /**
     * Clears cache and resets stats
     */
    reset() {
        this.cache.clear();
        this.errors = [];
        this.requestCount = 0;
        this.lastRequestTime = 0;
        this.totalBytes = 0;
    }

    /**
     * Estimates time to process domains
     * @param {number} domainCount - Number of domains
     * @returns {Object} Time estimation
     */
    estimateProcessingTime(domainCount) {
        const requestsPerSecond = this.config.rateLimit.requestsPerSecond;
        const totalSeconds = Math.ceil(domainCount / requestsPerSecond);
        
        return {
            totalSeconds,
            totalMinutes: Math.ceil(totalSeconds / 60),
            totalHours: Math.ceil(totalSeconds / 3600),
            formatted: this.formatDuration(totalSeconds)
        };
    }

    /**
     * Formats duration in human readable format
     * @param {number} seconds - Duration in seconds
     * @returns {string} Formatted duration
     */
    formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours}h ${minutes}m ${secs}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    }
}
