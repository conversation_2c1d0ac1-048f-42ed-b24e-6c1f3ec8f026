@echo off
echo ========================================
echo Domain Availability Checker - Manual Install
echo ========================================
echo.
echo This script installs packages one by one to avoid conflicts
echo.

echo [1/10] Updating pip...
python -m pip install --upgrade pip

echo.
echo [2/10] Installing FastAPI...
pip install fastapi==0.100.0

echo.
echo [3/10] Installing Uvicorn...
pip install uvicorn==0.22.0

echo.
echo [4/10] Installing Selenium...
pip install selenium==4.10.0

echo.
echo [5/10] Installing WebDriver Manager...
pip install webdriver-manager==3.8.6

echo.
echo [6/10] Installing basic dependencies...
pip install python-multipart jinja2 aiofiles websockets python-dotenv httpx

echo.
echo [7/10] Installing Pydantic (older version)...
pip install pydantic==1.10.12

echo.
echo [8/10] Installing Pandas and Numpy...
pip install pandas numpy

echo.
echo [9/10] Installing Excel support...
pip install openpyxl

echo.
echo [10/10] Installing testing tools...
pip install pytest pytest-asyncio

echo.
echo Creating directories...
if not exist "uploads" mkdir uploads
if not exist "downloads" mkdir downloads
if not exist "temp" mkdir temp
if not exist "logs" mkdir logs

echo.
echo Creating environment file...
if not exist ".env" (
    copy .env.example .env
    echo Environment file created from template
) else (
    echo Environment file already exists
)

echo.
echo ========================================
echo Manual installation completed!
echo ========================================
echo.
echo To start the application:
echo   python run.py
echo.
echo Then open your browser and go to:
echo   http://localhost:8000
echo.
pause
