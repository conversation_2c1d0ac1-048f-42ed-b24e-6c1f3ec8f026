#!/usr/bin/env python3
"""
Quick installer for Domain Availability Checker
This script tries multiple installation methods
"""
import subprocess
import sys
import os

def run_command(cmd, description):
    """Run a command and return success status"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} - Success")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - Failed")
        print(f"Error: {e.stderr}")
        return False

def install_package(package, description=None):
    """Install a single package"""
    desc = description or f"Installing {package}"
    return run_command(f"pip install {package}", desc)

def main():
    print("🚀 Domain Availability Checker - Quick Installer")
    print("=" * 50)
    
    # Check Python version
    print(f"Python version: {sys.version}")
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required!")
        return False
    
    # Update pip
    run_command("python -m pip install --upgrade pip", "Updating pip")
    
    # Core packages first
    packages = [
        ("fastapi==0.100.0", "FastAPI web framework"),
        ("uvicorn==0.22.0", "ASGI server"),
        ("selenium==4.10.0", "Web automation"),
        ("webdriver-manager==3.8.6", "WebDriver management"),
        ("python-multipart", "File upload support"),
        ("jinja2", "Template engine"),
        ("aiofiles", "Async file operations"),
        ("websockets", "WebSocket support"),
        ("python-dotenv", "Environment variables"),
        ("httpx", "HTTP client"),
        ("pydantic==1.10.12", "Data validation"),
    ]
    
    # Install core packages
    success_count = 0
    for package, desc in packages:
        if install_package(package, desc):
            success_count += 1
    
    # Try to install pandas/numpy
    data_packages = ["pandas", "numpy", "openpyxl"]
    for package in data_packages:
        install_package(package, f"Data processing - {package}")
    
    # Optional testing packages
    test_packages = ["pytest", "pytest-asyncio"]
    for package in test_packages:
        install_package(package, f"Testing - {package}")
    
    # Create directories
    print("\n📁 Creating directories...")
    dirs = ["uploads", "downloads", "temp", "logs"]
    for dir_name in dirs:
        os.makedirs(dir_name, exist_ok=True)
        print(f"✅ Created {dir_name}/")
    
    # Create .env file
    print("\n⚙️ Setting up environment...")
    if not os.path.exists(".env"):
        if os.path.exists(".env.example"):
            import shutil
            shutil.copy(".env.example", ".env")
            print("✅ Created .env from template")
        else:
            # Create basic .env
            with open(".env", "w") as f:
                f.write("# Domain Availability Checker Settings\n")
                f.write("DEBUG=False\n")
                f.write("HEADLESS_MODE=True\n")
                f.write("SELENIUM_TIMEOUT=30\n")
            print("✅ Created basic .env file")
    else:
        print("✅ .env file already exists")
    
    print("\n" + "=" * 50)
    if success_count >= 8:  # Most core packages installed
        print("🎉 Installation completed successfully!")
        print("\nTo start the application:")
        print("  python run.py")
        print("\nThen open your browser and go to:")
        print("  http://localhost:8000")
    else:
        print("⚠️ Installation completed with some issues")
        print("You may need to install missing packages manually")
        print("\nTry running:")
        print("  pip install fastapi uvicorn selenium pandas")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ Installation cancelled by user")
    except Exception as e:
        print(f"\n❌ Installation failed: {e}")
    
    input("\nPress Enter to continue...")
