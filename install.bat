@echo off
echo ========================================
echo Domain Availability Checker - Installer
echo ========================================
echo.

echo [1/5] Checking Python version...
python --version
if %errorlevel% neq 0 (
    echo ERROR: Python not found! Please install Python 3.8+ first.
    pause
    exit /b 1
)

echo.
echo [2/5] Updating pip...
python -m pip install --upgrade pip

echo.
echo [3/5] Installing core dependencies first...
pip install fastapi==0.100.0 uvicorn==0.22.0 selenium==4.10.0

echo.
echo [4/5] Installing remaining dependencies...
pip install python-multipart jinja2 aiofiles websockets python-dotenv httpx webdriver-manager
pip install pandas numpy openpyxl pytest pytest-asyncio

echo.
echo If above failed, trying alternative method...
pip install -r requirements-simple.txt

echo.
echo [5/5] Creating directories...
if not exist "uploads" mkdir uploads
if not exist "downloads" mkdir downloads
if not exist "temp" mkdir temp
if not exist "logs" mkdir logs

echo.
echo Creating environment file...
if not exist ".env" (
    copy .env.example .env
    echo Environment file created from template
) else (
    echo Environment file already exists
)

echo.
echo ========================================
echo Installation completed successfully!
echo ========================================
echo.
echo To start the application:
echo   python run.py
echo.
echo Then open your browser and go to:
echo   http://localhost:8000
echo.
pause
