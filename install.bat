@echo off
echo ========================================
echo Domain Availability Checker - Installer
echo ========================================
echo.

echo [1/4] Updating pip...
python -m pip install --upgrade pip

echo.
echo [2/4] Installing dependencies (minimal version for Windows)...
pip install -r requirements-minimal.txt

echo.
echo [3/4] Creating directories...
if not exist "uploads" mkdir uploads
if not exist "downloads" mkdir downloads
if not exist "temp" mkdir temp
if not exist "logs" mkdir logs

echo.
echo [4/4] Creating environment file...
if not exist ".env" (
    copy .env.example .env
    echo Environment file created from template
) else (
    echo Environment file already exists
)

echo.
echo ========================================
echo Installation completed successfully!
echo ========================================
echo.
echo To start the application:
echo   python run.py
echo.
echo Then open your browser and go to:
echo   http://localhost:8000
echo.
pause
