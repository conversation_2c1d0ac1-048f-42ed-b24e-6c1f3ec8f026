"""
Setup script for Domain Availability Checker
"""
from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="domain-availability-checker",
    version="1.0.0",
    author="Domain Checker Team",
    author_email="<EMAIL>",
    description="Automated domain availability checking using Namecheap bulk search",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/example/domain-availability-checker",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.4.3",
            "pytest-asyncio>=0.21.1",
            "pytest-cov>=4.1.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "domain-checker=app.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "app": [
            "templates/*.html",
            "static/css/*.css",
            "static/js/*.js",
        ],
    },
)
