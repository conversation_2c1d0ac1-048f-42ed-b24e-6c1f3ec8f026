/**
 * Rate Limiter Service
 * Handles rate limiting for API requests with strict compliance
 */
export class RateLimiter {
    constructor(options = {}) {
        this.config = {
            // Wayback Machine rate limits (strict compliance)
            search_calls_per_second: 0.8,      // 1.25s delay between calls
            memento_calls_per_second: 8,       // 125ms delay
            timemap_calls_per_second: 1.33,    // 750ms delay
            
            // Retry configuration
            maxRetries: 3,
            retryDelays: [2000, 4000, 8000],   // Exponential backoff
            
            // Burst control
            burstLimit: 5,
            burstWindow: 60000,                // 1 minute window
            
            // Circuit breaker
            circuitBreakerThreshold: 10,       // Failures before opening
            circuitBreakerTimeout: 30000,      // 30s before retry
            
            ...options
        };

        // State tracking
        this.requestLog = [];
        this.burstCount = 0;
        this.burstWindowStart = 0;
        this.circuitBreakerFailures = 0;
        this.circuitBreakerOpenTime = 0;
        this.isCircuitOpen = false;
        
        // Statistics
        this.stats = {
            totalRequests: 0,
            rateLimitHits: 0,
            retries: 0,
            circuitBreakerTrips: 0,
            averageDelay: 0
        };
    }

    /**
     * Waits for rate limit before allowing request
     * @param {string} apiType - Type of API call (search, memento, timemap)
     * @returns {Promise<Object>} Rate limit result
     */
    async waitForRateLimit(apiType = 'search') {
        const startTime = Date.now();
        
        try {
            // Check circuit breaker
            await this.checkCircuitBreaker();
            
            // Check burst limit
            await this.checkBurstLimit();
            
            // Apply rate limit for specific API type
            const delay = await this.calculateDelay(apiType);
            
            if (delay > 0) {
                await this.sleep(delay);
                this.stats.rateLimitHits++;
            }
            
            // Record request
            this.recordRequest(apiType);
            
            const totalDelay = Date.now() - startTime;
            this.updateAverageDelay(totalDelay);
            
            return {
                success: true,
                delay: totalDelay,
                apiType,
                timestamp: Date.now()
            };
            
        } catch (error) {
            return {
                success: false,
                error: error.message,
                delay: Date.now() - startTime,
                apiType,
                timestamp: Date.now()
            };
        }
    }

    /**
     * Calculates required delay for API type
     * @param {string} apiType - API type
     * @returns {Promise<number>} Required delay in ms
     */
    async calculateDelay(apiType) {
        const now = Date.now();
        const rateConfig = this.getRateConfig(apiType);
        const minInterval = 1000 / rateConfig.callsPerSecond;
        
        // Find relevant requests in the time window
        const relevantRequests = this.requestLog.filter(req => 
            req.apiType === apiType && 
            now - req.timestamp < rateConfig.window
        );
        
        if (relevantRequests.length === 0) {
            return 0;
        }
        
        // Calculate time since last request of this type
        const lastRequest = relevantRequests
            .sort((a, b) => b.timestamp - a.timestamp)[0];
        
        const timeSinceLastRequest = now - lastRequest.timestamp;
        
        if (timeSinceLastRequest < minInterval) {
            return Math.ceil(minInterval - timeSinceLastRequest);
        }
        
        // Check if we're exceeding the rate within the window
        const windowStart = now - rateConfig.window;
        const requestsInWindow = relevantRequests.filter(req => 
            req.timestamp >= windowStart
        );
        
        if (requestsInWindow.length >= rateConfig.maxRequests) {
            // Need to wait until the oldest request in window expires
            const oldestRequest = requestsInWindow
                .sort((a, b) => a.timestamp - b.timestamp)[0];
            
            const waitTime = rateConfig.window - (now - oldestRequest.timestamp);
            return Math.max(0, waitTime);
        }
        
        return 0;
    }

    /**
     * Gets rate configuration for API type
     * @param {string} apiType - API type
     * @returns {Object} Rate configuration
     */
    getRateConfig(apiType) {
        const configs = {
            search: {
                callsPerSecond: this.config.search_calls_per_second,
                maxRequests: Math.floor(this.config.search_calls_per_second * 60),
                window: 60000 // 1 minute
            },
            memento: {
                callsPerSecond: this.config.memento_calls_per_second,
                maxRequests: Math.floor(this.config.memento_calls_per_second * 60),
                window: 60000
            },
            timemap: {
                callsPerSecond: this.config.timemap_calls_per_second,
                maxRequests: Math.floor(this.config.timemap_calls_per_second * 60),
                window: 60000
            }
        };
        
        return configs[apiType] || configs.search;
    }

    /**
     * Checks and manages circuit breaker state
     * @throws {Error} If circuit is open
     */
    async checkCircuitBreaker() {
        if (!this.isCircuitOpen) {
            return;
        }
        
        const now = Date.now();
        
        // Check if circuit breaker timeout has passed
        if (now - this.circuitBreakerOpenTime >= this.config.circuitBreakerTimeout) {
            this.resetCircuitBreaker();
            return;
        }
        
        const remainingTime = this.config.circuitBreakerTimeout - (now - this.circuitBreakerOpenTime);
        throw new Error(`Circuit breaker is open. Retry in ${Math.ceil(remainingTime / 1000)}s`);
    }

    /**
     * Checks burst limit
     * @throws {Error} If burst limit exceeded
     */
    async checkBurstLimit() {
        const now = Date.now();
        
        // Reset burst window if needed
        if (now - this.burstWindowStart >= this.config.burstWindow) {
            this.burstCount = 0;
            this.burstWindowStart = now;
        }
        
        if (this.burstCount >= this.config.burstLimit) {
            const remainingTime = this.config.burstWindow - (now - this.burstWindowStart);
            throw new Error(`Burst limit exceeded. Wait ${Math.ceil(remainingTime / 1000)}s`);
        }
    }

    /**
     * Records a request
     * @param {string} apiType - API type
     */
    recordRequest(apiType) {
        const now = Date.now();
        
        this.requestLog.push({
            apiType,
            timestamp: now
        });
        
        // Increment burst counter
        this.burstCount++;
        
        // Update stats
        this.stats.totalRequests++;
        
        // Clean old requests (keep last hour)
        this.cleanOldRequests(now - 3600000);
    }

    /**
     * Cleans old requests from log
     * @param {number} cutoffTime - Timestamp cutoff
     */
    cleanOldRequests(cutoffTime) {
        this.requestLog = this.requestLog.filter(req => 
            req.timestamp > cutoffTime
        );
    }

    /**
     * Handles rate limit response (429 error)
     * @param {Object} response - HTTP response
     * @param {string} apiType - API type
     * @returns {Promise<number>} Retry delay
     */
    async handleRateLimitResponse(response, apiType) {
        this.stats.rateLimitHits++;
        
        // Get retry-after header
        const retryAfter = response.headers?.get?.('retry-after') || 
                          response.headers?.get?.('Retry-After');
        
        if (retryAfter) {
            const delay = parseInt(retryAfter) * 1000; // Convert to ms
            return Math.min(delay, 60000); // Cap at 60 seconds
        }
        
        // Use exponential backoff
        const retryCount = this.getRetryCount(apiType);
        return this.config.retryDelays[Math.min(retryCount, this.config.retryDelays.length - 1)];
    }

    /**
     * Gets retry count for API type
     * @param {string} apiType - API type
     * @returns {number} Retry count
     */
    getRetryCount(apiType) {
        const recentFailures = this.requestLog.filter(req => 
            req.apiType === apiType && 
            req.failed && 
            Date.now() - req.timestamp < 300000 // Last 5 minutes
        );
        
        return recentFailures.length;
    }

    /**
     * Records a failed request
     * @param {string} apiType - API type
     * @param {Error} error - Error details
     */
    recordFailure(apiType, error) {
        this.circuitBreakerFailures++;
        
        // Update request log
        const lastRequest = this.requestLog[this.requestLog.length - 1];
        if (lastRequest && lastRequest.apiType === apiType) {
            lastRequest.failed = true;
            lastRequest.error = error.message;
        }
        
        // Check if circuit breaker should open
        if (this.circuitBreakerFailures >= this.config.circuitBreakerThreshold) {
            this.openCircuitBreaker();
        }
        
        this.stats.retries++;
    }

    /**
     * Records a successful request
     * @param {string} apiType - API type
     */
    recordSuccess(apiType) {
        // Reset circuit breaker failures on success
        this.circuitBreakerFailures = Math.max(0, this.circuitBreakerFailures - 1);
        
        // Update request log
        const lastRequest = this.requestLog[this.requestLog.length - 1];
        if (lastRequest && lastRequest.apiType === apiType) {
            lastRequest.success = true;
        }
    }

    /**
     * Opens circuit breaker
     */
    openCircuitBreaker() {
        this.isCircuitOpen = true;
        this.circuitBreakerOpenTime = Date.now();
        this.stats.circuitBreakerTrips++;
        
        console.warn('Circuit breaker opened due to excessive failures');
    }

    /**
     * Resets circuit breaker
     */
    resetCircuitBreaker() {
        this.isCircuitOpen = false;
        this.circuitBreakerOpenTime = 0;
        this.circuitBreakerFailures = 0;
        
        console.info('Circuit breaker reset');
    }

    /**
     * Updates average delay statistics
     * @param {number} delay - Request delay
     */
    updateAverageDelay(delay) {
        const totalRequests = this.stats.totalRequests;
        this.stats.averageDelay = (
            (this.stats.averageDelay * (totalRequests - 1)) + delay
        ) / totalRequests;
    }

    /**
     * Sleeps for specified duration
     * @param {number} ms - Milliseconds to sleep
     * @returns {Promise<void>}
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Gets current rate limit status
     * @param {string} apiType - API type
     * @returns {Object} Rate limit status
     */
    getRateLimitStatus(apiType = 'search') {
        const now = Date.now();
        const rateConfig = this.getRateConfig(apiType);
        
        // Count requests in current window
        const windowStart = now - rateConfig.window;
        const requestsInWindow = this.requestLog.filter(req => 
            req.apiType === apiType && 
            req.timestamp >= windowStart
        ).length;
        
        const remaining = Math.max(0, rateConfig.maxRequests - requestsInWindow);
        const resetTime = windowStart + rateConfig.window;
        
        return {
            apiType,
            limit: rateConfig.maxRequests,
            remaining,
            resetTime,
            resetIn: Math.max(0, resetTime - now),
            circuitOpen: this.isCircuitOpen,
            burstRemaining: Math.max(0, this.config.burstLimit - this.burstCount)
        };
    }

    /**
     * Gets comprehensive statistics
     * @returns {Object} Statistics
     */
    getStats() {
        const now = Date.now();
        
        // Calculate requests per minute
        const lastMinute = this.requestLog.filter(req => 
            now - req.timestamp < 60000
        ).length;
        
        // Calculate success rate
        const recentRequests = this.requestLog.filter(req => 
            now - req.timestamp < 300000 // Last 5 minutes
        );
        
        const successfulRequests = recentRequests.filter(req => req.success).length;
        const successRate = recentRequests.length > 0 ? 
            (successfulRequests / recentRequests.length) * 100 : 100;
        
        return {
            ...this.stats,
            requestsPerMinute: lastMinute,
            successRate: Math.round(successRate),
            circuitBreakerOpen: this.isCircuitOpen,
            burstCount: this.burstCount,
            requestLogSize: this.requestLog.length,
            uptime: now - (this.requestLog[0]?.timestamp || now)
        };
    }

    /**
     * Estimates wait time for a batch of requests
     * @param {number} requestCount - Number of requests
     * @param {string} apiType - API type
     * @returns {Object} Time estimation
     */
    estimateBatchTime(requestCount, apiType = 'search') {
        const rateConfig = this.getRateConfig(apiType);
        const totalSeconds = Math.ceil(requestCount / rateConfig.callsPerSecond);
        
        return {
            totalSeconds,
            totalMinutes: Math.ceil(totalSeconds / 60),
            totalHours: Math.ceil(totalSeconds / 3600),
            requestsPerSecond: rateConfig.callsPerSecond,
            formatted: this.formatDuration(totalSeconds)
        };
    }

    /**
     * Formats duration in human readable format
     * @param {number} seconds - Duration in seconds
     * @returns {string} Formatted duration
     */
    formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;

        if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else if (minutes > 0) {
            return `${minutes}m ${secs}s`;
        } else {
            return `${secs}s`;
        }
    }

    /**
     * Resets all rate limiting state
     */
    reset() {
        this.requestLog = [];
        this.burstCount = 0;
        this.burstWindowStart = 0;
        this.circuitBreakerFailures = 0;
        this.circuitBreakerOpenTime = 0;
        this.isCircuitOpen = false;
        
        this.stats = {
            totalRequests: 0,
            rateLimitHits: 0,
            retries: 0,
            circuitBreakerTrips: 0,
            averageDelay: 0
        };
    }

    /**
     * Updates rate limit configuration
     * @param {Object} newConfig - New configuration
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
    }

    /**
     * Checks if a request would be rate limited
     * @param {string} apiType - API type
     * @returns {Object} Check result
     */
    wouldBeRateLimited(apiType = 'search') {
        const status = this.getRateLimitStatus(apiType);
        const delay = this.calculateDelay(apiType);
        
        return {
            wouldWait: delay > 0,
            delay,
            remaining: status.remaining,
            circuitOpen: this.isCircuitOpen,
            burstLimited: this.burstCount >= this.config.burstLimit
        };
    }
}
