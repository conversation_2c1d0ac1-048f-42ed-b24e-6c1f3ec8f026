# Troubleshooting Guide

## Lỗi cài đặt dependencies

### Lỗi pandas/numpy trên Windows

**Triệu chứng:**
```
error: subprocess-exited-with-error
× pip subprocess to install build dependencies did not run successfully
```

**Giải pháp:**

1. **Sử dụng requirements-minimal.txt:**
   ```cmd
   pip install -r requirements-minimal.txt
   ```

2. **Cài đặt từng package riêng lẻ:**
   ```cmd
   pip install pandas numpy
   pip install fastapi uvicorn selenium
   pip install python-dotenv pydantic-settings
   ```

3. **Sử dụng conda (nếu có Anaconda):**
   ```cmd
   conda install pandas numpy
   pip install -r requirements-minimal.txt
   ```

4. **Cài đặt Visual Studio Build Tools:**
   - Download từ: https://visualstudio.microsoft.com/visual-cpp-build-tools/
   - Chọn "C++ build tools" khi cài đặt

### Lỗi Python version

**Triệu chứng:**
```
ERROR: Package requires a different Python
```

**Giải pháp:**
- <PERSON><PERSON><PERSON> bảo sử dụng Python 3.8 trở lên
- Kiểm tra version: `python --version`

## Lỗi Selenium/Chrome

### Chrome WebDriver không tìm thấy

**Triệu chứng:**
```
selenium.common.exceptions.WebDriverException: Message: 'chromedriver' executable needs to be in PATH
```

**Giải pháp:**
1. Cài đặt Google Chrome browser
2. WebDriver sẽ tự động download qua webdriver-manager
3. Nếu vẫn lỗi, set path trong .env:
   ```env
   CHROME_DRIVER_PATH="C:/path/to/chromedriver.exe"
   ```

### Selenium timeout

**Triệu chứng:**
```
selenium.common.exceptions.TimeoutException
```

**Giải pháp:**
1. Tăng timeout trong UI hoặc .env:
   ```env
   SELENIUM_TIMEOUT=60
   PAGE_LOAD_TIMEOUT=120
   ```

2. Kiểm tra kết nối internet
3. Thử chế độ non-headless để debug

### Chrome không khởi động được

**Triệu chứng:**
```
selenium.common.exceptions.WebDriverException: Message: unknown error: Chrome failed to start
```

**Giải pháp:**
1. Chạy với quyền Administrator (Windows)
2. Thêm vào .env:
   ```env
   HEADLESS_MODE=True
   ```
3. Kiểm tra Chrome có bị block bởi antivirus

## Lỗi file permissions

### Không thể tạo thư mục

**Triệu chứng:**
```
PermissionError: [Errno 13] Permission denied
```

**Giải pháp:**
1. Chạy terminal/cmd với quyền Administrator
2. Tạo thư mục thủ công:
   ```cmd
   mkdir uploads downloads temp logs
   ```

### Không thể download file

**Triệu chứng:**
File CSV không được tải xuống

**Giải pháp:**
1. Kiểm tra quyền ghi thư mục downloads/
2. Tạo thư mục nếu chưa có:
   ```cmd
   mkdir downloads
   ```

## Lỗi ứng dụng

### Port 8000 đã được sử dụng

**Triệu chứng:**
```
OSError: [Errno 48] Address already in use
```

**Giải pháp:**
1. Đổi port trong run.py:
   ```python
   uvicorn.run("app.main:app", port=8001)
   ```

2. Hoặc kill process đang dùng port 8000:
   ```cmd
   # Windows
   netstat -ano | findstr :8000
   taskkill /PID <PID> /F
   
   # Linux/Mac
   lsof -ti:8000 | xargs kill -9
   ```

### WebSocket connection failed

**Triệu chứng:**
Progress không update real-time

**Giải pháp:**
1. Refresh trang web
2. Kiểm tra firewall/antivirus
3. Thử truy cập trực tiếp: ws://localhost:8000/ws/general

## Lỗi Namecheap

### Popup không đóng được

**Triệu chứng:**
Selenium bị stuck ở popup

**Giải pháp:**
1. Chạy ở chế độ non-headless để xem popup
2. Update selector trong config.py nếu Namecheap thay đổi

### Không tìm thấy element

**Triệu chứng:**
```
selenium.common.exceptions.NoSuchElementException
```

**Giải pháp:**
1. Namecheap có thể đã thay đổi giao diện
2. Update XPath selectors trong config.py
3. Tăng timeout để đợi element load

## Debug tips

### Chế độ debug

1. Bỏ tick "Chế độ headless" trong UI
2. Tick "Chế độ debug"
3. Xem log chi tiết trong console

### Kiểm tra log files

```cmd
# Xem log mới nhất
type logs\domain_checker_*.log

# Linux/Mac
tail -f logs/domain_checker_*.log
```

### Test API trực tiếp

```bash
curl -X GET "http://localhost:8000/health"
curl -X GET "http://localhost:8000/docs"
```

## Liên hệ hỗ trợ

Nếu vẫn gặp vấn đề:
1. Tạo issue trên GitHub với log lỗi đầy đủ
2. Bao gồm thông tin:
   - OS version
   - Python version
   - Chrome version
   - Log files
   - Steps to reproduce
