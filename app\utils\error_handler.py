"""
Error handling utilities for the domain checker application
"""
import functools
import asyncio
import time
from typing import Callable, Any, Optional, Type, Union
from selenium.common.exceptions import (
    TimeoutException, NoSuchElementException, WebDriverException,
    ElementClickInterceptedException, StaleElementReferenceException
)

from app.utils.logger import get_logger

logger = get_logger(__name__)

class DomainCheckerError(Exception):
    """Base exception for domain checker application"""
    pass

class SeleniumError(DomainCheckerError):
    """Selenium-related errors"""
    pass

class CSVProcessingError(DomainCheckerError):
    """CSV processing errors"""
    pass

class JobManagerError(DomainCheckerError):
    """Job management errors"""
    pass

class RetryConfig:
    """Configuration for retry mechanism"""
    def __init__(
        self,
        max_attempts: int = 3,
        delay: float = 1.0,
        backoff_factor: float = 2.0,
        max_delay: float = 60.0,
        exceptions: tuple = (Exception,)
    ):
        self.max_attempts = max_attempts
        self.delay = delay
        self.backoff_factor = backoff_factor
        self.max_delay = max_delay
        self.exceptions = exceptions

def retry_on_exception(config: RetryConfig = None):
    """Decorator for retrying functions on specific exceptions"""
    if config is None:
        config = RetryConfig()
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            delay = config.delay
            
            for attempt in range(config.max_attempts):
                try:
                    return func(*args, **kwargs)
                except config.exceptions as e:
                    last_exception = e
                    
                    if attempt == config.max_attempts - 1:
                        logger.error(f"Function {func.__name__} failed after {config.max_attempts} attempts: {str(e)}")
                        raise e
                    
                    logger.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {str(e)}. Retrying in {delay}s...")
                    time.sleep(delay)
                    delay = min(delay * config.backoff_factor, config.max_delay)
            
            raise last_exception
        
        return wrapper
    return decorator

def async_retry_on_exception(config: RetryConfig = None):
    """Async decorator for retrying functions on specific exceptions"""
    if config is None:
        config = RetryConfig()
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            delay = config.delay
            
            for attempt in range(config.max_attempts):
                try:
                    return await func(*args, **kwargs)
                except config.exceptions as e:
                    last_exception = e
                    
                    if attempt == config.max_attempts - 1:
                        logger.error(f"Async function {func.__name__} failed after {config.max_attempts} attempts: {str(e)}")
                        raise e
                    
                    logger.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {str(e)}. Retrying in {delay}s...")
                    await asyncio.sleep(delay)
                    delay = min(delay * config.backoff_factor, config.max_delay)
            
            raise last_exception
        
        return wrapper
    return decorator

# Predefined retry configurations
SELENIUM_RETRY_CONFIG = RetryConfig(
    max_attempts=3,
    delay=2.0,
    backoff_factor=1.5,
    exceptions=(
        TimeoutException,
        NoSuchElementException,
        ElementClickInterceptedException,
        StaleElementReferenceException,
        WebDriverException
    )
)

NETWORK_RETRY_CONFIG = RetryConfig(
    max_attempts=5,
    delay=1.0,
    backoff_factor=2.0,
    exceptions=(ConnectionError, TimeoutError)
)

CSV_RETRY_CONFIG = RetryConfig(
    max_attempts=2,
    delay=0.5,
    exceptions=(IOError, OSError, PermissionError)
)

def handle_selenium_error(func: Callable) -> Callable:
    """Decorator specifically for handling Selenium errors"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except TimeoutException as e:
            logger.error(f"Selenium timeout in {func.__name__}: {str(e)}")
            raise SeleniumError(f"Timeout waiting for element: {str(e)}")
        except NoSuchElementException as e:
            logger.error(f"Element not found in {func.__name__}: {str(e)}")
            raise SeleniumError(f"Element not found: {str(e)}")
        except ElementClickInterceptedException as e:
            logger.error(f"Element click intercepted in {func.__name__}: {str(e)}")
            raise SeleniumError(f"Element click intercepted: {str(e)}")
        except WebDriverException as e:
            logger.error(f"WebDriver error in {func.__name__}: {str(e)}")
            raise SeleniumError(f"WebDriver error: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected error in {func.__name__}: {str(e)}")
            raise SeleniumError(f"Unexpected Selenium error: {str(e)}")
    
    return wrapper

def safe_execute(func: Callable, default_return: Any = None, log_errors: bool = True) -> Any:
    """Safely execute a function and return default value on error"""
    try:
        return func()
    except Exception as e:
        if log_errors:
            logger.error(f"Error in safe_execute: {str(e)}")
        return default_return

async def async_safe_execute(func: Callable, default_return: Any = None, log_errors: bool = True) -> Any:
    """Safely execute an async function and return default value on error"""
    try:
        return await func()
    except Exception as e:
        if log_errors:
            logger.error(f"Error in async_safe_execute: {str(e)}")
        return default_return

class ErrorContext:
    """Context manager for error handling with cleanup"""
    
    def __init__(self, operation_name: str, cleanup_func: Optional[Callable] = None):
        self.operation_name = operation_name
        self.cleanup_func = cleanup_func
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        logger.info(f"Starting operation: {self.operation_name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time if self.start_time else 0
        
        if exc_type is None:
            logger.info(f"Operation completed successfully: {self.operation_name} (took {duration:.2f}s)")
        else:
            logger.error(f"Operation failed: {self.operation_name} (took {duration:.2f}s) - {str(exc_val)}")
            
            # Execute cleanup if provided
            if self.cleanup_func:
                try:
                    self.cleanup_func()
                    logger.info(f"Cleanup completed for: {self.operation_name}")
                except Exception as cleanup_error:
                    logger.error(f"Cleanup failed for {self.operation_name}: {str(cleanup_error)}")
        
        # Don't suppress the exception
        return False

def validate_domain_format(domain: str) -> bool:
    """Validate domain format"""
    import re
    
    if not domain or not isinstance(domain, str):
        return False
    
    domain = domain.strip().lower()
    
    # Basic domain validation regex
    domain_pattern = re.compile(
        r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
    )
    
    return bool(domain_pattern.match(domain))

def sanitize_filename(filename: str) -> str:
    """Sanitize filename to prevent path traversal and invalid characters"""
    import re
    import os
    
    # Remove path separators and invalid characters
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    filename = re.sub(r'\.\.', '_', filename)  # Prevent path traversal
    
    # Ensure filename is not empty and not too long
    filename = filename.strip()
    if not filename:
        filename = "unnamed_file"
    
    # Limit filename length
    if len(filename) > 255:
        name, ext = os.path.splitext(filename)
        filename = name[:255-len(ext)] + ext
    
    return filename

class CircuitBreaker:
    """Circuit breaker pattern for handling repeated failures"""
    
    def __init__(self, failure_threshold: int = 5, timeout: float = 60.0):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'closed'  # closed, open, half-open
    
    def call(self, func: Callable, *args, **kwargs):
        """Call function with circuit breaker protection"""
        if self.state == 'open':
            if time.time() - self.last_failure_time > self.timeout:
                self.state = 'half-open'
                logger.info("Circuit breaker moving to half-open state")
            else:
                raise DomainCheckerError("Circuit breaker is open")
        
        try:
            result = func(*args, **kwargs)
            self.on_success()
            return result
        except Exception as e:
            self.on_failure()
            raise e
    
    def on_success(self):
        """Handle successful call"""
        self.failure_count = 0
        if self.state == 'half-open':
            self.state = 'closed'
            logger.info("Circuit breaker closed after successful call")
    
    def on_failure(self):
        """Handle failed call"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = 'open'
            logger.warning(f"Circuit breaker opened after {self.failure_count} failures")
