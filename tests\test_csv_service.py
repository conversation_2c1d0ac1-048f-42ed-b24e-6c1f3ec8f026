"""
Tests for CSV service
"""
import pytest
import pandas as pd
import tempfile
import os
from pathlib import Path

from app.services.csv_service import CSVProcessor
from app.models.domain_models import DomainResult

class TestCSVProcessor:
    """Test CSV processing service"""
    
    @pytest.fixture
    def csv_processor(self):
        """Create CSV processor instance"""
        return CSVProcessor()
    
    @pytest.fixture
    def sample_csv_files(self):
        """Create sample CSV files for testing"""
        files = []
        
        # Create first CSV file
        data1 = {
            'Domain': ['example1.com', 'example2.com', 'example3.com'],
            'Available': [True, False, True],
            'Price': ['$10.99', None, '$12.99'],
            'Currency': ['USD', 'USD', 'USD'],
            'Registrar': ['Namecheap', 'Namecheap', 'Namecheap']
        }
        df1 = pd.DataFrame(data1)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            df1.to_csv(f.name, index=False)
            files.append(f.name)
        
        # Create second CSV file
        data2 = {
            'Domain': ['example4.com', 'example5.com', 'example1.com'],  # example1.com is duplicate
            'Available': [False, True, True],
            'Price': [None, '$15.99', '$10.99'],
            'Currency': ['USD', 'USD', 'USD'],
            'Registrar': ['Namecheap', 'Namecheap', 'Namecheap']
        }
        df2 = pd.DataFrame(data2)
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            df2.to_csv(f.name, index=False)
            files.append(f.name)
        
        yield files
        
        # Cleanup
        for file_path in files:
            if os.path.exists(file_path):
                os.unlink(file_path)
    
    @pytest.mark.asyncio
    async def test_merge_csv_files(self, csv_processor, sample_csv_files):
        """Test merging CSV files"""
        output_filename = "test_merged.csv"
        
        try:
            merged_file = await csv_processor.merge_csv_files(
                sample_csv_files, output_filename
            )
            
            assert os.path.exists(merged_file)
            
            # Read merged file and verify content
            df = pd.read_csv(merged_file)
            
            # Should have 5 unique domains (duplicate removed)
            assert len(df) == 5
            
            # Check that all expected domains are present
            expected_domains = ['example1.com', 'example2.com', 'example3.com', 'example4.com', 'example5.com']
            assert set(df['Domain'].tolist()) == set(expected_domains)
            
        finally:
            # Cleanup
            merged_path = csv_processor.download_dir / output_filename
            if merged_path.exists():
                merged_path.unlink()
    
    @pytest.mark.asyncio
    async def test_parse_results_file(self, csv_processor, sample_csv_files):
        """Test parsing results file"""
        # Use first sample file
        results = await csv_processor.parse_results_file(sample_csv_files[0])
        
        assert len(results) == 3
        
        # Check first result
        first_result = results[0]
        assert isinstance(first_result, DomainResult)
        assert first_result.domain == 'example1.com'
        assert first_result.available is True
        assert first_result.price == '$10.99'
        assert first_result.currency == 'USD'
        assert first_result.registrar == 'Namecheap'
        
        # Check second result (unavailable)
        second_result = results[1]
        assert second_result.domain == 'example2.com'
        assert second_result.available is False
        assert second_result.price is None
    
    @pytest.mark.asyncio
    async def test_export_results_to_csv(self, csv_processor):
        """Test exporting results to CSV"""
        # Create sample results
        results = [
            DomainResult(
                domain="test1.com",
                available=True,
                price="$10.99",
                currency="USD",
                registrar="Namecheap"
            ),
            DomainResult(
                domain="test2.com",
                available=False,
                registrar="Namecheap"
            ),
            DomainResult(
                domain="test3.com",
                error="Timeout error"
            )
        ]
        
        filename = "test_export.csv"
        
        try:
            file_path = await csv_processor.export_results_to_csv(results, filename)
            
            assert os.path.exists(file_path)
            
            # Read and verify exported file
            df = pd.read_csv(file_path)
            assert len(df) == 3
            
            # Check columns
            expected_columns = ['Domain', 'Available', 'Price', 'Currency', 'Registrar', 'Error']
            assert list(df.columns) == expected_columns
            
            # Check first row
            assert df.iloc[0]['Domain'] == 'test1.com'
            assert df.iloc[0]['Available'] is True
            assert df.iloc[0]['Price'] == '$10.99'
            
            # Check error row
            assert df.iloc[2]['Domain'] == 'test3.com'
            assert df.iloc[2]['Error'] == 'Timeout error'
            
        finally:
            # Cleanup
            export_path = csv_processor.download_dir / filename
            if export_path.exists():
                export_path.unlink()
    
    def test_get_file_info(self, csv_processor, sample_csv_files):
        """Test getting file information"""
        file_info = csv_processor.get_file_info(sample_csv_files[0])
        
        assert 'error' not in file_info
        assert file_info['row_count'] == 3
        assert file_info['column_count'] == 5
        assert 'Domain' in file_info['columns']
        assert 'Available' in file_info['columns']
        assert len(file_info['sample_data']) == 3
    
    def test_get_file_info_nonexistent(self, csv_processor):
        """Test getting info for non-existent file"""
        file_info = csv_processor.get_file_info("nonexistent.csv")
        
        assert 'error' in file_info
        assert file_info['error'] == "File not found"
    
    def test_cleanup_temp_files(self, csv_processor):
        """Test cleanup of temporary files"""
        # Create temporary files
        temp_files = []
        for i in range(3):
            with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
                f.write("test,data\n1,2\n")
                temp_files.append(f.name)
        
        # Verify files exist
        for file_path in temp_files:
            assert os.path.exists(file_path)
        
        # Cleanup
        csv_processor.cleanup_temp_files(temp_files)
        
        # Verify files are deleted
        for file_path in temp_files:
            assert not os.path.exists(file_path)
