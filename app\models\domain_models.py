"""
Pydantic models for domain checking API
"""
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator
from enum import Enum
import re

class JobStatus(str, Enum):
    """Job status enumeration"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class DomainCheckRequest(BaseModel):
    """Request model for domain checking"""
    domains: List[str] = Field(..., description="List of domains to check")
    job_name: Optional[str] = Field(None, description="Optional job name")
    
    @validator('domains')
    def validate_domains(cls, v):
        """Validate domain format"""
        if not v:
            raise ValueError("Domain list cannot be empty")
        
        if len(v) > 50000:  # Reasonable limit
            raise ValueError("Too many domains. Maximum 50,000 domains per request")
        
        # Basic domain validation
        domain_pattern = re.compile(
            r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
        )
        
        validated_domains = []
        for domain in v:
            domain = domain.strip().lower()
            if domain and domain_pattern.match(domain):
                validated_domains.append(domain)
        
        if not validated_domains:
            raise ValueError("No valid domains found")
        
        return validated_domains

class DomainResult(BaseModel):
    """Individual domain result"""
    domain: str
    available: Optional[bool] = None
    price: Optional[str] = None
    currency: Optional[str] = None
    registrar: Optional[str] = None
    error: Optional[str] = None

class BatchInfo(BaseModel):
    """Batch processing information"""
    batch_id: str
    total_domains: int
    processed_domains: int = 0
    status: JobStatus = JobStatus.PENDING
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    error_message: Optional[str] = None

class JobProgress(BaseModel):
    """Job progress information"""
    job_id: str
    job_name: Optional[str] = None
    status: JobStatus
    total_domains: int
    processed_domains: int = 0
    total_batches: int
    completed_batches: int = 0
    current_batch: Optional[BatchInfo] = None
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    progress_percentage: float = 0.0
    estimated_time_remaining: Optional[int] = None  # seconds
    error_message: Optional[str] = None
    results_file_url: Optional[str] = None

class JobResponse(BaseModel):
    """Response model for job creation"""
    job_id: str
    message: str
    total_domains: int
    total_batches: int
    status: JobStatus

class DomainCheckResults(BaseModel):
    """Complete results for domain checking"""
    job_id: str
    job_name: Optional[str] = None
    total_domains: int
    processed_domains: int
    results: List[DomainResult]
    status: JobStatus
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    processing_time: Optional[float] = None  # seconds
    download_url: Optional[str] = None

class WebSocketMessage(BaseModel):
    """WebSocket message model"""
    type: str  # progress, error, complete, etc.
    job_id: str
    data: Dict[str, Any]
    timestamp: str

class ErrorResponse(BaseModel):
    """Error response model"""
    error: str
    detail: Optional[str] = None
    job_id: Optional[str] = None
