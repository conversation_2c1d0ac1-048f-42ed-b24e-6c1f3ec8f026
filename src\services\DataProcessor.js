/**
 * Data Processor Service
 * Handles processing and transformation of Wayback Machine data
 */
export class DataProcessor {
    constructor(options = {}) {
        this.config = {
            batchSize: 10,
            maxConcurrent: 5,
            retryAttempts: 3,
            retryDelay: 2000,
            processTimeout: 30000,
            cacheResults: true,
            ...options
        };

        // Processing state
        this.isProcessing = false;
        this.isPaused = false;
        this.currentBatch = 0;
        this.processedCount = 0;
        this.failedCount = 0;
        this.startTime = null;
        this.queue = [];
        this.results = [];
        this.errors = [];
        
        // Event handlers
        this.onProgress = null;
        this.onBatchComplete = null;
        this.onComplete = null;
        this.onError = null;
        this.onPause = null;
        this.onResume = null;
    }

    /**
     * Processes a batch of domains
     * @param {Array<string>} domains - Domains to process
     * @param {Object} options - Processing options
     * @returns {Promise<Object>} Processing results
     */
    async processDomains(domains, options = {}) {
        const opts = {
            batchSize: this.config.batchSize,
            maxConcurrent: this.config.maxConcurrent,
            apiService: null,
            rateLimiter: null,
            progressive: true,
            ...options
        };

        if (!opts.apiService) {
            throw new Error('API service is required');
        }

        if (!opts.rateLimiter) {
            throw new Error('Rate limiter is required');
        }

        // Validate inputs
        if (!Array.isArray(domains) || domains.length === 0) {
            throw new Error('Domains array is required and cannot be empty');
        }

        // Initialize processing state
        this.initializeProcessing(domains, opts);

        try {
            // Process domains in batches
            while (this.hasMoreToProcess() && !this.isPaused) {
                await this.processBatch(opts);
            }

            // Complete processing
            return this.completeProcessing();
            
        } catch (error) {
            this.handleProcessingError(error);
            throw error;
        }
    }

    /**
     * Initializes processing state
     * @param {Array<string>} domains - Domains to process
     * @param {Object} options - Processing options
     */
    initializeProcessing(domains, options) {
        this.isProcessing = true;
        this.isPaused = false;
        this.currentBatch = 0;
        this.processedCount = 0;
        this.failedCount = 0;
        this.startTime = Date.now();
        this.queue = [...domains];
        this.results = [];
        this.errors = [];

        this.emitProgress('started', {
            totalDomains: domains.length,
            batchSize: options.batchSize
        });
    }

    /**
     * Processes a single batch
     * @param {Object} options - Processing options
     * @returns {Promise<void>}
     */
    async processBatch(options) {
        const batchDomains = this.queue.splice(0, options.batchSize);
        
        if (batchDomains.length === 0) {
            return;
        }

        this.currentBatch++;
        
        this.emitProgress('batch_started', {
            batchNumber: this.currentBatch,
            batchSize: batchDomains.length,
            domains: batchDomains
        });

        try {
            // Process domains concurrently with limit
            const batchResults = await this.processConcurrentBatch(
                batchDomains, 
                options
            );

            // Add results
            this.results.push(...batchResults.successes);
            this.errors.push(...batchResults.failures);
            
            this.processedCount += batchDomains.length;
            this.failedCount += batchResults.failures.length;

            this.emitProgress('batch_completed', {
                batchNumber: this.currentBatch,
                processedCount: this.processedCount,
                failedCount: this.failedCount,
                remainingCount: this.queue.length,
                results: batchResults
            });

            if (this.onBatchComplete) {
                this.onBatchComplete(batchResults, this.getProgress());
            }

        } catch (error) {
            this.handleBatchError(error, batchDomains);
        }
    }

    /**
     * Processes domains concurrently with limit
     * @param {Array<string>} domains - Domains to process
     * @param {Object} options - Processing options
     * @returns {Promise<Object>} Batch results
     */
    async processConcurrentBatch(domains, options) {
        const { maxConcurrent, apiService, rateLimiter } = options;
        const successes = [];
        const failures = [];
        
        // Create chunks for concurrent processing
        const chunks = this.chunkArray(domains, maxConcurrent);
        
        for (const chunk of chunks) {
            if (this.isPaused) {
                break;
            }

            // Process chunk concurrently
            const chunkPromises = chunk.map(domain => 
                this.processSingleDomain(domain, apiService, rateLimiter)
            );

            const chunkResults = await Promise.allSettled(chunkPromises);
            
            // Categorize results
            chunkResults.forEach((result, index) => {
                const domain = chunk[index];
                
                if (result.status === 'fulfilled') {
                    successes.push({
                        domain,
                        ...result.value
                    });
                } else {
                    failures.push({
                        domain,
                        error: result.reason.message || 'Unknown error',
                        timestamp: Date.now()
                    });
                }
            });
        }

        return { successes, failures };
    }

    /**
     * Processes a single domain
     * @param {string} domain - Domain to process
     * @param {Object} apiService - API service
     * @param {Object} rateLimiter - Rate limiter
     * @returns {Promise<Object>} Domain result
     */
    async processSingleDomain(domain, apiService, rateLimiter) {
        const startTime = Date.now();
        
        try {
            // Apply rate limiting
            await rateLimiter.waitForRateLimit('search');
            
            // Make API request
            const response = await apiService.searchDomain(domain);
            
            if (!response.success) {
                throw new Error(response.error?.message || 'API request failed');
            }

            // Process the response data
            const processedData = apiService.processSnapshots(response.data, domain);
            
            // Record success
            rateLimiter.recordSuccess('search');
            
            const endTime = Date.now();
            
            return {
                ...processedData,
                responseTime: endTime - startTime,
                fromCache: response.fromCache || false,
                timestamp: endTime
            };
            
        } catch (error) {
            // Record failure
            rateLimiter.recordFailure('search', error);
            
            const endTime = Date.now();
            
            // Return error result
            return {
                domain,
                snapshotCount: 0,
                snapshots: [],
                dateRange: null,
                statusCodes: {},
                firstSnapshot: null,
                lastSnapshot: null,
                totalSize: 0,
                error: error.message,
                responseTime: endTime - startTime,
                timestamp: endTime
            };
        }
    }

    /**
     * Pauses processing
     */
    pause() {
        if (!this.isProcessing) {
            return;
        }

        this.isPaused = true;
        this.emitProgress('paused', this.getProgress());
        
        if (this.onPause) {
            this.onPause(this.getProgress());
        }
    }

    /**
     * Resumes processing
     * @param {Object} options - Resume options
     * @returns {Promise<Object>} Processing results
     */
    async resume(options = {}) {
        if (!this.isPaused) {
            return this.getProgress();
        }

        this.isPaused = false;
        this.emitProgress('resumed', this.getProgress());
        
        if (this.onResume) {
            this.onResume(this.getProgress());
        }

        try {
            // Continue processing
            while (this.hasMoreToProcess() && !this.isPaused) {
                await this.processBatch(options);
            }

            return this.completeProcessing();
            
        } catch (error) {
            this.handleProcessingError(error);
            throw error;
        }
    }

    /**
     * Stops processing
     */
    stop() {
        this.isProcessing = false;
        this.isPaused = false;
        
        this.emitProgress('stopped', this.getProgress());
    }

    /**
     * Checks if there's more to process
     * @returns {boolean} True if more processing needed
     */
    hasMoreToProcess() {
        return this.isProcessing && this.queue.length > 0;
    }

    /**
     * Completes processing
     * @returns {Object} Final results
     */
    completeProcessing() {
        const endTime = Date.now();
        const duration = endTime - this.startTime;
        
        this.isProcessing = false;
        this.isPaused = false;

        const finalResults = {
            completed: true,
            totalProcessed: this.processedCount,
            successCount: this.results.length,
            failureCount: this.failedCount,
            duration,
            averageTimePerDomain: this.processedCount > 0 ? duration / this.processedCount : 0,
            results: this.results,
            errors: this.errors,
            stats: this.getStats()
        };

        this.emitProgress('completed', finalResults);
        
        if (this.onComplete) {
            this.onComplete(finalResults);
        }

        return finalResults;
    }

    /**
     * Handles processing error
     * @param {Error} error - Error to handle
     */
    handleProcessingError(error) {
        this.isProcessing = false;
        this.isPaused = false;
        
        const errorInfo = {
            error: error.message,
            timestamp: Date.now(),
            processedCount: this.processedCount,
            failedCount: this.failedCount
        };

        this.emitProgress('error', errorInfo);
        
        if (this.onError) {
            this.onError(error, this.getProgress());
        }
    }

    /**
     * Handles batch error
     * @param {Error} error - Error to handle
     * @param {Array<string>} domains - Domains in failed batch
     */
    handleBatchError(error, domains) {
        // Mark all domains in batch as failed
        const batchFailures = domains.map(domain => ({
            domain,
            error: error.message,
            timestamp: Date.now()
        }));

        this.errors.push(...batchFailures);
        this.failedCount += domains.length;
        this.processedCount += domains.length;

        console.error('Batch processing error:', error);
    }

    /**
     * Gets current progress
     * @returns {Object} Progress information
     */
    getProgress() {
        const totalDomains = this.processedCount + this.queue.length;
        const completionPercentage = totalDomains > 0 ? 
            Math.round((this.processedCount / totalDomains) * 100) : 0;

        const now = Date.now();
        const elapsed = this.startTime ? now - this.startTime : 0;
        const averageTimePerDomain = this.processedCount > 0 ? elapsed / this.processedCount : 0;
        const remainingTime = this.queue.length * averageTimePerDomain;

        return {
            isProcessing: this.isProcessing,
            isPaused: this.isPaused,
            totalDomains,
            processedCount: this.processedCount,
            successCount: this.results.length,
            failureCount: this.failedCount,
            remainingCount: this.queue.length,
            completionPercentage,
            currentBatch: this.currentBatch,
            elapsed,
            estimatedTimeRemaining: remainingTime,
            averageTimePerDomain,
            domainsPerSecond: elapsed > 0 ? (this.processedCount / (elapsed / 1000)) : 0
        };
    }

    /**
     * Gets comprehensive statistics
     * @returns {Object} Statistics
     */
    getStats() {
        const progress = this.getProgress();
        
        // Calculate success rate
        const successRate = this.processedCount > 0 ? 
            (this.results.length / this.processedCount) * 100 : 0;

        // Calculate average response time
        const responseTimes = this.results
            .filter(r => r.responseTime)
            .map(r => r.responseTime);
        
        const averageResponseTime = responseTimes.length > 0 ?
            responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length : 0;

        // Calculate average snapshots per domain
        const snapshotCounts = this.results
            .filter(r => !r.error)
            .map(r => r.snapshotCount);
        
        const averageSnapshots = snapshotCounts.length > 0 ?
            snapshotCounts.reduce((sum, count) => sum + count, 0) / snapshotCounts.length : 0;

        // Group results by status codes
        const statusCodeStats = {};
        this.results.forEach(result => {
            if (result.statusCodes) {
                Object.entries(result.statusCodes).forEach(([code, count]) => {
                    statusCodeStats[code] = (statusCodeStats[code] || 0) + count;
                });
            }
        });

        return {
            ...progress,
            successRate: Math.round(successRate),
            averageResponseTime: Math.round(averageResponseTime),
            averageSnapshots: Math.round(averageSnapshots),
            statusCodeStats,
            totalSnapshots: snapshotCounts.reduce((sum, count) => sum + count, 0),
            domainsWithSnapshots: snapshotCounts.filter(count => count > 0).length,
            errorTypes: this.getErrorTypes()
        };
    }

    /**
     * Gets error type statistics
     * @returns {Object} Error type counts
     */
    getErrorTypes() {
        const errorTypes = {};
        
        this.errors.forEach(error => {
            const errorType = this.categorizeError(error.error);
            errorTypes[errorType] = (errorTypes[errorType] || 0) + 1;
        });

        return errorTypes;
    }

    /**
     * Categorizes error type
     * @param {string} errorMessage - Error message
     * @returns {string} Error category
     */
    categorizeError(errorMessage) {
        const msg = errorMessage.toLowerCase();
        
        if (msg.includes('timeout')) return 'Timeout';
        if (msg.includes('network')) return 'Network';
        if (msg.includes('rate limit') || msg.includes('429')) return 'Rate Limit';
        if (msg.includes('404')) return 'Not Found';
        if (msg.includes('500')) return 'Server Error';
        if (msg.includes('invalid domain')) return 'Invalid Domain';
        
        return 'Other';
    }

    /**
     * Emits progress event
     * @param {string} type - Event type
     * @param {Object} data - Event data
     */
    emitProgress(type, data) {
        if (this.onProgress) {
            this.onProgress(type, data);
        }
    }

    /**
     * Chunks array into smaller arrays
     * @param {Array} array - Array to chunk
     * @param {number} size - Chunk size
     * @returns {Array<Array>} Chunked arrays
     */
    chunkArray(array, size) {
        const chunks = [];
        for (let i = 0; i < array.length; i += size) {
            chunks.push(array.slice(i, i + size));
        }
        return chunks;
    }

    /**
     * Formats results for export
     * @param {string} format - Export format
     * @returns {Array} Formatted results
     */
    formatResults(format = 'default') {
        return this.results.map((result, index) => {
            const formatted = {
                stt: index + 1,
                domain: result.domain,
                snapshot: result.error ? 'Error' : result.snapshotCount,
                timeRange: this.formatTimeRange(result),
                statusCodes: this.formatStatusCodes(result.statusCodes, result.error)
            };

            if (format === 'detailed') {
                formatted.firstSnapshot = result.firstSnapshot || 'N/A';
                formatted.lastSnapshot = result.lastSnapshot || 'N/A';
                formatted.totalSize = result.totalSize || 0;
                formatted.responseTime = result.responseTime || 0;
                formatted.fromCache = result.fromCache || false;
                formatted.error = result.error || null;
            }

            return formatted;
        });
    }

    /**
     * Formats time range for display
     * @param {Object} result - Domain result
     * @returns {string} Formatted time range
     */
    formatTimeRange(result) {
        if (result.error) {
            return 'N/A';
        }
        
        if (!result.firstSnapshot || !result.lastSnapshot) {
            return 'N/A';
        }
        
        if (result.firstSnapshot === result.lastSnapshot) {
            return result.firstSnapshot;
        }
        
        return `${result.firstSnapshot} - ${result.lastSnapshot}`;
    }

    /**
     * Formats status codes for display
     * @param {Object} statusCodes - Status code counts
     * @param {string} error - Error message
     * @returns {string} Formatted status codes
     */
    formatStatusCodes(statusCodes, error) {
        if (error) {
            return error;
        }
        
        if (!statusCodes || Object.keys(statusCodes).length === 0) {
            return 'N/A';
        }
        
        return Object.entries(statusCodes)
            .sort(([a], [b]) => a.localeCompare(b))
            .map(([code, count]) => `${code}:${count}`)
            .join(' - ');
    }

    /**
     * Resets processor state
     */
    reset() {
        this.isProcessing = false;
        this.isPaused = false;
        this.currentBatch = 0;
        this.processedCount = 0;
        this.failedCount = 0;
        this.startTime = null;
        this.queue = [];
        this.results = [];
        this.errors = [];
    }

    /**
     * Exports results to specified format
     * @param {string} format - Export format (csv, json, etc.)
     * @param {Object} options - Export options
     * @returns {string} Exported data
     */
    exportResults(format = 'csv', options = {}) {
        const formattedResults = this.formatResults(options.detailed ? 'detailed' : 'default');
        
        switch (format.toLowerCase()) {
            case 'csv':
                return this.exportToCSV(formattedResults);
            case 'json':
                return JSON.stringify(formattedResults, null, 2);
            case 'tsv':
                return this.exportToTSV(formattedResults);
            default:
                throw new Error(`Unsupported export format: ${format}`);
        }
    }

    /**
     * Exports results to CSV format
     * @param {Array} results - Formatted results
     * @returns {string} CSV content
     */
    exportToCSV(results) {
        if (results.length === 0) return '';
        
        const headers = Object.keys(results[0]);
        const csvRows = [headers.join(',')];
        
        results.forEach(result => {
            const row = headers.map(header => {
                const value = result[header];
                return `"${String(value).replace(/"/g, '""')}"`;
            });
            csvRows.push(row.join(','));
        });
        
        return csvRows.join('\n');
    }

    /**
     * Exports results to TSV format
     * @param {Array} results - Formatted results
     * @returns {string} TSV content
     */
    exportToTSV(results) {
        if (results.length === 0) return '';
        
        const headers = Object.keys(results[0]);
        const tsvRows = [headers.join('\t')];
        
        results.forEach(result => {
            const row = headers.map(header => String(result[header]));
            tsvRows.push(row.join('\t'));
        });
        
        return tsvRows.join('\n');
    }
}
