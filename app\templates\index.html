<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Domain Availability Checker</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8" x-data="domainChecker()">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-800 mb-2">
                <i class="fas fa-globe text-blue-600"></i>
                Domain Availability Checker
            </h1>
            <p class="text-gray-600">Kiểm tra tính khả dụng của domain hàng loạt với Namecheap</p>
        </div>

        <!-- Main Content -->
        <div class="max-w-4xl mx-auto">
            <!-- Input Section -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">
                    <i class="fas fa-list text-green-600"></i>
                    Nhập danh sách Domain
                </h2>
                
                <div class="mb-4">
                    <label for="jobName" class="block text-sm font-medium text-gray-700 mb-2">
                        Tên công việc (tùy chọn)
                    </label>
                    <input 
                        type="text" 
                        id="jobName" 
                        x-model="jobName"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Ví dụ: Kiểm tra domain tháng 12/2024"
                    >
                </div>

                <div class="mb-4">
                    <label for="domainList" class="block text-sm font-medium text-gray-700 mb-2">
                        Danh sách Domain (mỗi domain một dòng)
                    </label>
                    <textarea 
                        id="domainList" 
                        x-model="domainList"
                        rows="10" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="example1.com&#10;example2.net&#10;example3.org&#10;..."
                    ></textarea>
                    <div class="mt-2 text-sm text-gray-500">
                        <span x-text="domainCount"></span> domain được nhập
                    </div>
                </div>

                <button 
                    @click="startDomainCheck()"
                    :disabled="!domainList.trim() || isProcessing"
                    class="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-semibold py-3 px-6 rounded-md transition duration-200"
                >
                    <i class="fas fa-search mr-2"></i>
                    <span x-show="!isProcessing">Kiểm tra Domain</span>
                    <span x-show="isProcessing">Đang xử lý...</span>
                </button>
            </div>

            <!-- Progress Section -->
            <div x-show="currentJob" class="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">
                    <i class="fas fa-chart-line text-orange-600"></i>
                    Tiến độ xử lý
                </h2>

                <!-- Job Info -->
                <div class="mb-4">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm font-medium text-gray-700">Job ID:</span>
                        <span class="text-sm text-gray-600" x-text="currentJob?.job_id"></span>
                    </div>
                    <div class="flex justify-between items-center mb-2" x-show="currentJob?.job_name">
                        <span class="text-sm font-medium text-gray-700">Tên công việc:</span>
                        <span class="text-sm text-gray-600" x-text="currentJob?.job_name"></span>
                    </div>
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm font-medium text-gray-700">Trạng thái:</span>
                        <span class="text-sm px-2 py-1 rounded-full" 
                              :class="getStatusClass(currentJob?.status)" 
                              x-text="getStatusText(currentJob?.status)">
                        </span>
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="mb-4">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm font-medium text-gray-700">Tiến độ tổng thể</span>
                        <span class="text-sm text-gray-600" x-text="Math.round(currentJob?.progress_percentage || 0) + '%'"></span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-3">
                        <div class="bg-blue-600 h-3 rounded-full transition-all duration-300" 
                             :style="'width: ' + (currentJob?.progress_percentage || 0) + '%'">
                        </div>
                    </div>
                </div>

                <!-- Batch Progress -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600" x-text="currentJob?.processed_domains || 0"></div>
                        <div class="text-sm text-gray-600">Domain đã xử lý</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600" x-text="currentJob?.completed_batches || 0"></div>
                        <div class="text-sm text-gray-600">Batch hoàn thành</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-orange-600" x-text="currentJob?.estimated_time_remaining ? formatTime(currentJob.estimated_time_remaining) : 'N/A'"></div>
                        <div class="text-sm text-gray-600">Thời gian còn lại</div>
                    </div>
                </div>

                <!-- Current Batch Info -->
                <div x-show="currentJob?.current_batch" class="bg-gray-50 rounded-md p-4">
                    <h3 class="font-semibold text-gray-800 mb-2">Batch hiện tại</h3>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="font-medium">Batch ID:</span>
                            <span x-text="currentJob?.current_batch?.batch_id"></span>
                        </div>
                        <div>
                            <span class="font-medium">Trạng thái:</span>
                            <span x-text="getStatusText(currentJob?.current_batch?.status)"></span>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="mt-4 flex gap-2">
                    <button 
                        @click="cancelJob()"
                        x-show="currentJob?.status === 'processing' || currentJob?.status === 'pending'"
                        class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md transition duration-200"
                    >
                        <i class="fas fa-stop mr-2"></i>
                        Hủy công việc
                    </button>
                    
                    <button 
                        @click="downloadResults()"
                        x-show="currentJob?.status === 'completed' && currentJob?.results_file_url"
                        class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md transition duration-200"
                    >
                        <i class="fas fa-download mr-2"></i>
                        Tải kết quả CSV
                    </button>
                </div>
            </div>

            <!-- Results Section -->
            <div x-show="results.length > 0" class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">
                    <i class="fas fa-table text-purple-600"></i>
                    Kết quả kiểm tra
                </h2>

                <div class="mb-4">
                    <div class="text-sm text-gray-600">
                        Hiển thị <span x-text="Math.min(displayLimit, results.length)"></span> / <span x-text="results.length"></span> kết quả
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full table-auto">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-4 py-2 text-left text-sm font-medium text-gray-700">Domain</th>
                                <th class="px-4 py-2 text-left text-sm font-medium text-gray-700">Trạng thái</th>
                                <th class="px-4 py-2 text-left text-sm font-medium text-gray-700">Giá</th>
                                <th class="px-4 py-2 text-left text-sm font-medium text-gray-700">Registrar</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-200">
                            <template x-for="result in results.slice(0, displayLimit)" :key="result.domain">
                                <tr class="hover:bg-gray-50">
                                    <td class="px-4 py-2 text-sm text-gray-900" x-text="result.domain"></td>
                                    <td class="px-4 py-2 text-sm">
                                        <span class="px-2 py-1 rounded-full text-xs"
                                              :class="result.available ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                                              x-text="result.available ? 'Có sẵn' : 'Không có sẵn'">
                                        </span>
                                    </td>
                                    <td class="px-4 py-2 text-sm text-gray-900" x-text="result.price || 'N/A'"></td>
                                    <td class="px-4 py-2 text-sm text-gray-900" x-text="result.registrar || 'N/A'"></td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>

                <div x-show="results.length > displayLimit" class="mt-4 text-center">
                    <button 
                        @click="displayLimit += 50"
                        class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md transition duration-200"
                    >
                        Hiển thị thêm
                    </button>
                </div>
            </div>
        </div>

        <!-- Toast Notifications -->
        <div x-show="notification.show" 
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform translate-y-2"
             x-transition:enter-end="opacity-100 transform translate-y-0"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100 transform translate-y-0"
             x-transition:leave-end="opacity-0 transform translate-y-2"
             class="fixed top-4 right-4 z-50">
            <div class="rounded-md p-4 shadow-lg"
                 :class="notification.type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'">
                <div class="flex items-center">
                    <i :class="notification.type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-circle'" class="mr-2"></i>
                    <span x-text="notification.message"></span>
                </div>
            </div>
        </div>
    </div>

    <script src="/static/js/domain-checker.js"></script>
</body>
</html>
