"""
Domain checker API routes
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from fastapi.responses import FileResponse
from typing import List, Dict
import uuid
import asyncio
from datetime import datetime

from app.models.domain_models import (
    DomainCheckRequest, JobResponse, JobProgress, 
    DomainCheckResults, ErrorResponse, JobStatus
)
from app.services.domain_service import DomainCheckService
from app.services.job_manager import JobManager
from app.utils.logger import get_logger

router = APIRouter()
logger = get_logger(__name__)

# Global instances
job_manager = JobManager()
domain_service = DomainCheckService()

@router.post("/check-domains", response_model=JobResponse)
async def start_domain_check(
    request: DomainCheckRequest,
    background_tasks: BackgroundTasks
):
    """
    Start domain availability checking job
    """
    try:
        # Generate job ID
        job_id = str(uuid.uuid4())
        
        # Calculate batches
        total_batches = len(request.domains) // 5000 + (1 if len(request.domains) % 5000 else 0)
        
        # Create job
        job_progress = JobProgress(
            job_id=job_id,
            job_name=request.job_name,
            status=JobStatus.PENDING,
            total_domains=len(request.domains),
            total_batches=total_batches,
            start_time=datetime.now().isoformat()
        )
        
        # Store job
        job_manager.create_job(job_id, job_progress)
        
        # Start background processing
        background_tasks.add_task(
            process_domain_check_job,
            job_id,
            request.domains,
            request.job_name,
            request.selenium_options.dict() if request.selenium_options else None
        )
        
        logger.info(f"Started domain check job {job_id} with {len(request.domains)} domains")
        
        return JobResponse(
            job_id=job_id,
            message="Domain checking job started successfully",
            total_domains=len(request.domains),
            total_batches=total_batches,
            status=JobStatus.PENDING
        )
        
    except Exception as e:
        logger.error(f"Error starting domain check job: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/job/{job_id}/progress", response_model=JobProgress)
async def get_job_progress(job_id: str):
    """
    Get job progress information
    """
    try:
        progress = job_manager.get_job_progress(job_id)
        if not progress:
            raise HTTPException(status_code=404, detail="Job not found")
        
        return progress
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job progress for {job_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/job/{job_id}/results", response_model=DomainCheckResults)
async def get_job_results(job_id: str):
    """
    Get job results
    """
    try:
        results = job_manager.get_job_results(job_id)
        if not results:
            raise HTTPException(status_code=404, detail="Job not found or not completed")
        
        return results
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job results for {job_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/job/{job_id}/download")
async def download_results(job_id: str):
    """
    Download job results as CSV file
    """
    try:
        file_path = job_manager.get_results_file_path(job_id)
        if not file_path:
            raise HTTPException(status_code=404, detail="Results file not found")
        
        return FileResponse(
            path=file_path,
            filename=f"domain_check_results_{job_id}.csv",
            media_type="text/csv"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading results for {job_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/job/{job_id}")
async def cancel_job(job_id: str):
    """
    Cancel a running job
    """
    try:
        success = job_manager.cancel_job(job_id)
        if not success:
            raise HTTPException(status_code=404, detail="Job not found or cannot be cancelled")
        
        logger.info(f"Cancelled job {job_id}")
        return {"message": "Job cancelled successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling job {job_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/jobs", response_model=List[JobProgress])
async def list_jobs():
    """
    List all jobs
    """
    try:
        jobs = job_manager.list_jobs()
        return jobs
        
    except Exception as e:
        logger.error(f"Error listing jobs: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

async def process_domain_check_job(job_id: str, domains: List[str], job_name: str = None, selenium_options: dict = None):
    """
    Background task to process domain checking job
    """
    try:
        logger.info(f"Starting processing for job {job_id}")
        
        # Update job status
        job_manager.update_job_status(job_id, JobStatus.PROCESSING)
        
        # Process domains using domain service
        await domain_service.process_domains(job_id, domains, job_manager, selenium_options)
        
        # Mark job as completed
        job_manager.update_job_status(job_id, JobStatus.COMPLETED)
        
        logger.info(f"Completed processing for job {job_id}")
        
    except Exception as e:
        logger.error(f"Error processing job {job_id}: {str(e)}")
        job_manager.update_job_status(job_id, JobStatus.FAILED, str(e))
