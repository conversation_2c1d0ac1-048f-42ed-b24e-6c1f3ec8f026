"""
WebSocket handler for real-time updates
"""
from fastapi import API<PERSON>outer, WebSocket, WebSocketDisconnect
from typing import Dict, Set
import json
import asyncio
from datetime import datetime

from app.models.domain_models import WebSocketMessage
from app.utils.logger import get_logger

router = APIRouter()
logger = get_logger(__name__)

class ConnectionManager:
    """Manages WebSocket connections"""
    
    def __init__(self):
        # Store active connections by job_id
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        # Store all connections for broadcasting
        self.all_connections: Set[WebSocket] = set()
    
    async def connect(self, websocket: WebSocket, job_id: str = None):
        """Accept new WebSocket connection"""
        await websocket.accept()
        self.all_connections.add(websocket)
        
        if job_id:
            if job_id not in self.active_connections:
                self.active_connections[job_id] = set()
            self.active_connections[job_id].add(websocket)
        
        logger.info(f"WebSocket connected for job: {job_id}")
    
    def disconnect(self, websocket: WebSocket, job_id: str = None):
        """Remove WebSocket connection"""
        self.all_connections.discard(websocket)
        
        if job_id and job_id in self.active_connections:
            self.active_connections[job_id].discard(websocket)
            if not self.active_connections[job_id]:
                del self.active_connections[job_id]
        
        logger.info(f"WebSocket disconnected for job: {job_id}")
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        """Send message to specific WebSocket"""
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"Error sending personal message: {str(e)}")
    
    async def send_job_update(self, job_id: str, message: WebSocketMessage):
        """Send update to all connections for a specific job"""
        if job_id not in self.active_connections:
            return
        
        message_text = message.json()
        disconnected = set()
        
        for connection in self.active_connections[job_id]:
            try:
                await connection.send_text(message_text)
            except Exception as e:
                logger.error(f"Error sending job update: {str(e)}")
                disconnected.add(connection)
        
        # Remove disconnected connections
        for connection in disconnected:
            self.disconnect(connection, job_id)
    
    async def broadcast(self, message: WebSocketMessage):
        """Broadcast message to all connections"""
        message_text = message.json()
        disconnected = set()
        
        for connection in self.all_connections:
            try:
                await connection.send_text(message_text)
            except Exception as e:
                logger.error(f"Error broadcasting message: {str(e)}")
                disconnected.add(connection)
        
        # Remove disconnected connections
        for connection in disconnected:
            self.all_connections.discard(connection)

# Global connection manager
manager = ConnectionManager()

@router.websocket("/progress/{job_id}")
async def websocket_job_progress(websocket: WebSocket, job_id: str):
    """WebSocket endpoint for job progress updates"""
    await manager.connect(websocket, job_id)
    
    try:
        # Send initial connection confirmation
        welcome_message = WebSocketMessage(
            type="connected",
            job_id=job_id,
            data={"message": f"Connected to job {job_id}"},
            timestamp=datetime.now().isoformat()
        )
        await manager.send_personal_message(welcome_message.json(), websocket)
        
        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for messages from client (like ping/pong)
                data = await asyncio.wait_for(websocket.receive_text(), timeout=30.0)
                
                # Handle client messages if needed
                try:
                    client_message = json.loads(data)
                    if client_message.get("type") == "ping":
                        pong_message = WebSocketMessage(
                            type="pong",
                            job_id=job_id,
                            data={"timestamp": datetime.now().isoformat()},
                            timestamp=datetime.now().isoformat()
                        )
                        await manager.send_personal_message(pong_message.json(), websocket)
                except json.JSONDecodeError:
                    pass  # Ignore invalid JSON
                    
            except asyncio.TimeoutError:
                # Send periodic heartbeat
                heartbeat_message = WebSocketMessage(
                    type="heartbeat",
                    job_id=job_id,
                    data={"timestamp": datetime.now().isoformat()},
                    timestamp=datetime.now().isoformat()
                )
                await manager.send_personal_message(heartbeat_message.json(), websocket)
                
    except WebSocketDisconnect:
        manager.disconnect(websocket, job_id)
        logger.info(f"WebSocket disconnected for job {job_id}")
    except Exception as e:
        logger.error(f"WebSocket error for job {job_id}: {str(e)}")
        manager.disconnect(websocket, job_id)

@router.websocket("/general")
async def websocket_general(websocket: WebSocket):
    """General WebSocket endpoint for system updates"""
    await manager.connect(websocket)
    
    try:
        # Send initial connection confirmation
        welcome_message = WebSocketMessage(
            type="connected",
            job_id="system",
            data={"message": "Connected to general updates"},
            timestamp=datetime.now().isoformat()
        )
        await manager.send_personal_message(welcome_message.json(), websocket)
        
        # Keep connection alive
        while True:
            try:
                data = await asyncio.wait_for(websocket.receive_text(), timeout=30.0)
                # Handle general messages if needed
            except asyncio.TimeoutError:
                # Send periodic heartbeat
                heartbeat_message = WebSocketMessage(
                    type="heartbeat",
                    job_id="system",
                    data={"timestamp": datetime.now().isoformat()},
                    timestamp=datetime.now().isoformat()
                )
                await manager.send_personal_message(heartbeat_message.json(), websocket)
                
    except WebSocketDisconnect:
        manager.disconnect(websocket)
        logger.info("General WebSocket disconnected")
    except Exception as e:
        logger.error(f"General WebSocket error: {str(e)}")
        manager.disconnect(websocket)

# Function to send updates from other parts of the application
async def send_job_progress_update(job_id: str, progress_data: dict):
    """Send progress update for a specific job"""
    message = WebSocketMessage(
        type="progress",
        job_id=job_id,
        data=progress_data,
        timestamp=datetime.now().isoformat()
    )
    await manager.send_job_update(job_id, message)

async def send_job_completion(job_id: str, results_data: dict):
    """Send job completion notification"""
    message = WebSocketMessage(
        type="completed",
        job_id=job_id,
        data=results_data,
        timestamp=datetime.now().isoformat()
    )
    await manager.send_job_update(job_id, message)

async def send_job_error(job_id: str, error_data: dict):
    """Send job error notification"""
    message = WebSocketMessage(
        type="error",
        job_id=job_id,
        data=error_data,
        timestamp=datetime.now().isoformat()
    )
    await manager.send_job_update(job_id, message)
