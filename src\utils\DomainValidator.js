/**
 * Domain Validator Utility
 * Validates domain format and provides domain-related utilities
 */
export class DomainValidator {
    constructor() {
        // RFC compliant domain regex
        this.domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*\.[a-zA-Z]{2,}$/;
        
        // Additional validation patterns
        this.patterns = {
            // Basic domain format
            basic: /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/,
            
            // Subdomain support
            subdomain: /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+$/,
            
            // International domain names (basic)
            idn: /^[a-zA-Z0-9\u00a1-\uffff]([a-zA-Z0-9-\u00a1-\uffff]{0,61}[a-zA-Z0-9\u00a1-\uffff])?(\.[a-zA-Z0-9\u00a1-\uffff]([a-zA-Z0-9-\u00a1-\uffff]{0,61}[a-zA-Z0-9\u00a1-\uffff])?)*\.[a-zA-Z\u00a1-\uffff]{2,}$/
        };
        
        // Common invalid patterns
        this.invalidPatterns = [
            /^-/,           // Starts with hyphen
            /-$/,           // Ends with hyphen
            /--/,           // Double hyphen
            /\.\./,         // Double dot
            /^\./, 
            /\.$/,          // Starts or ends with dot
            /\s/,           // Contains whitespace
            /_/             // Contains underscore (not allowed in domain names)
        ];
        
        // Reserved/special use domains
        this.reservedTlds = [
            'localhost',
            'local',
            'internal',
            'test',
            'invalid',
            'example',
            'arpa'
        ];
    }

    /**
     * Validates a single domain
     * @param {string} domain - Domain to validate
     * @param {Object} options - Validation options
     * @returns {Object} Validation result
     */
    validateDomain(domain, options = {}) {
        const opts = {
            allowSubdomains: true,
            allowInternational: true,
            checkReserved: true,
            maxLength: 253,
            ...options
        };

        const result = {
            isValid: false,
            domain: domain.trim(),
            errors: [],
            warnings: [],
            normalized: null
        };

        // Basic checks
        if (!domain || typeof domain !== 'string') {
            result.errors.push('Domain is required and must be a string');
            return result;
        }

        const trimmedDomain = domain.trim().toLowerCase();
        result.normalized = trimmedDomain;

        // Length check
        if (trimmedDomain.length === 0) {
            result.errors.push('Domain cannot be empty');
            return result;
        }

        if (trimmedDomain.length > opts.maxLength) {
            result.errors.push(`Domain exceeds maximum length of ${opts.maxLength} characters`);
            return result;
        }

        // Check for invalid patterns
        for (const pattern of this.invalidPatterns) {
            if (pattern.test(trimmedDomain)) {
                result.errors.push('Domain contains invalid characters or format');
                break;
            }
        }

        // Check for basic domain format
        if (!this.patterns.basic.test(trimmedDomain)) {
            // If basic fails, check if it's a subdomain
            if (opts.allowSubdomains && this.patterns.subdomain.test(trimmedDomain)) {
                // Valid subdomain
            } else if (opts.allowInternational && this.patterns.idn.test(trimmedDomain)) {
                // Valid international domain
            } else {
                result.errors.push('Invalid domain format');
            }
        }

        // Check for reserved TLDs
        if (opts.checkReserved) {
            const tld = trimmedDomain.split('.').pop();
            if (this.reservedTlds.includes(tld)) {
                result.warnings.push(`Domain uses reserved TLD: ${tld}`);
            }
        }

        // Additional domain-specific checks
        const parts = trimmedDomain.split('.');
        
        // Check each part length
        for (const part of parts) {
            if (part.length === 0) {
                result.errors.push('Domain contains empty labels');
                break;
            }
            if (part.length > 63) {
                result.errors.push('Domain label exceeds 63 characters');
                break;
            }
        }

        // Must have at least 2 parts (domain.tld)
        if (parts.length < 2) {
            result.errors.push('Domain must have at least a domain and TLD');
        }

        // TLD validation
        const tld = parts[parts.length - 1];
        if (tld.length < 2) {
            result.errors.push('TLD must be at least 2 characters');
        }

        result.isValid = result.errors.length === 0;
        return result;
    }

    /**
     * Validates multiple domains
     * @param {Array<string>} domains - Array of domains to validate
     * @param {Object} options - Validation options
     * @returns {Object} Batch validation result
     */
    validateDomains(domains, options = {}) {
        const opts = {
            maxDomains: 5000,
            skipDuplicates: true,
            ...options
        };

        const result = {
            total: domains.length,
            valid: [],
            invalid: [],
            duplicates: [],
            skipped: []
        };

        if (domains.length > opts.maxDomains) {
            throw new Error(`Too many domains. Maximum allowed: ${opts.maxDomains}`);
        }

        const seen = new Set();

        for (let i = 0; i < domains.length; i++) {
            const domain = domains[i];
            
            if (!domain || !domain.trim()) {
                result.skipped.push({ index: i, domain, reason: 'Empty domain' });
                continue;
            }

            const normalizedDomain = domain.trim().toLowerCase();

            // Check for duplicates
            if (opts.skipDuplicates) {
                if (seen.has(normalizedDomain)) {
                    result.duplicates.push({ index: i, domain, original: normalizedDomain });
                    continue;
                }
                seen.add(normalizedDomain);
            }

            const validation = this.validateDomain(domain, options);
            
            if (validation.isValid) {
                result.valid.push({
                    index: i,
                    domain: validation.normalized,
                    original: domain,
                    warnings: validation.warnings
                });
            } else {
                result.invalid.push({
                    index: i,
                    domain,
                    errors: validation.errors,
                    warnings: validation.warnings
                });
            }
        }

        return result;
    }

    /**
     * Normalizes a domain name
     * @param {string} domain - Domain to normalize
     * @returns {string} Normalized domain
     */
    normalizeDomain(domain) {
        if (!domain || typeof domain !== 'string') {
            return '';
        }

        return domain
            .trim()
            .toLowerCase()
            .replace(/^https?:\/\//, '')    // Remove protocol
            .replace(/^www\./, '')          // Remove www
            .replace(/\/.*$/, '')           // Remove path
            .replace(/:.*$/, '');           // Remove port
    }

    /**
     * Extracts domains from various text formats
     * @param {string} text - Text containing domains
     * @param {Object} options - Extraction options
     * @returns {Array<string>} Extracted domains
     */
    extractDomains(text, options = {}) {
        const opts = {
            format: 'auto',     // auto, list, csv, json
            removeProtocol: true,
            removeWww: true,
            removePath: true,
            unique: true,
            ...options
        };

        let domains = [];

        switch (opts.format) {
            case 'csv':
                domains = this.extractFromCSV(text);
                break;
            case 'json':
                domains = this.extractFromJSON(text);
                break;
            case 'list':
            case 'auto':
            default:
                domains = this.extractFromList(text);
                break;
        }

        // Apply normalization options
        if (opts.removeProtocol || opts.removeWww || opts.removePath) {
            domains = domains.map(domain => {
                let normalized = domain;
                if (opts.removeProtocol) {
                    normalized = normalized.replace(/^https?:\/\//, '');
                }
                if (opts.removeWww) {
                    normalized = normalized.replace(/^www\./, '');
                }
                if (opts.removePath) {
                    normalized = normalized.replace(/\/.*$/, '');
                }
                return normalized;
            });
        }

        // Remove duplicates
        if (opts.unique) {
            domains = [...new Set(domains.map(d => d.toLowerCase()))];
        }

        return domains.filter(domain => domain.trim().length > 0);
    }

    /**
     * Extracts domains from list format (one per line)
     * @param {string} text - Text in list format
     * @returns {Array<string>} Extracted domains
     */
    extractFromList(text) {
        return text
            .split(/[\r\n]+/)
            .map(line => line.trim())
            .filter(line => line.length > 0);
    }

    /**
     * Extracts domains from CSV format
     * @param {string} text - Text in CSV format
     * @returns {Array<string>} Extracted domains
     */
    extractFromCSV(text) {
        const domains = [];
        const lines = text.split(/[\r\n]+/);
        
        for (const line of lines) {
            if (line.trim()) {
                // Simple CSV parsing - take first column
                const match = line.match(/^"?([^",\r\n]+)"?/);
                if (match) {
                    domains.push(match[1].trim());
                }
            }
        }
        
        return domains;
    }

    /**
     * Extracts domains from JSON format
     * @param {string} text - Text in JSON format
     * @returns {Array<string>} Extracted domains
     */
    extractFromJSON(text) {
        try {
            const data = JSON.parse(text);
            
            if (Array.isArray(data)) {
                return data.map(item => 
                    typeof item === 'string' ? item : 
                    item.domain || item.url || item.name || ''
                ).filter(Boolean);
            }
            
            if (typeof data === 'object' && data !== null) {
                // Try to find domain arrays in object
                for (const key of ['domains', 'urls', 'sites', 'hosts']) {
                    if (Array.isArray(data[key])) {
                        return data[key].filter(item => typeof item === 'string');
                    }
                }
            }
            
            return [];
        } catch (error) {
            throw new Error('Invalid JSON format');
        }
    }

    /**
     * Gets validation statistics
     * @param {Object} validationResult - Result from validateDomains
     * @returns {Object} Statistics
     */
    getStats(validationResult) {
        const total = validationResult.total;
        const validCount = validationResult.valid.length;
        const invalidCount = validationResult.invalid.length;
        const duplicateCount = validationResult.duplicates.length;
        const skippedCount = validationResult.skipped.length;

        return {
            total,
            valid: validCount,
            invalid: invalidCount,
            duplicates: duplicateCount,
            skipped: skippedCount,
            processed: validCount + invalidCount,
            validPercentage: total > 0 ? Math.round((validCount / total) * 100) : 0,
            invalidPercentage: total > 0 ? Math.round((invalidCount / total) * 100) : 0
        };
    }

    /**
     * Formats validation errors for display
     * @param {Array} errors - Array of error objects
     * @returns {string} Formatted error message
     */
    formatErrors(errors) {
        return errors.map(error => {
            const parts = [
                `Line ${error.index + 1}`,
                `Domain: ${error.domain}`,
                `Errors: ${error.errors.join(', ')}`
            ];
            
            if (error.warnings && error.warnings.length > 0) {
                parts.push(`Warnings: ${error.warnings.join(', ')}`);
            }
            
            return parts.join(' | ');
        }).join('\n');
    }
}
