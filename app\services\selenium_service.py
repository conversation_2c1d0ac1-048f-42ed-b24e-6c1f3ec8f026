"""
Selenium automation service for Namecheap domain checking
"""
import time
import asyncio
import os
import json
from typing import List, Dict, Optional, Tuple
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import (
    TimeoutException, NoSuchElementException, 
    WebDriverException, ElementClickInterceptedException
)
from webdriver_manager.chrome import ChromeDriverManager

from app.config import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)

class NamecheapAutomation:
    """Selenium automation for Namecheap bulk domain search"""
    
    def __init__(self):
        self.driver: Optional[webdriver.Chrome] = None
        self.wait: Optional[WebDriverWait] = None
        self.download_dir = os.path.abspath(settings.download_dir)
        
    def setup_driver(self) -> webdriver.Chrome:
        """Setup Chrome WebDriver with appropriate options"""
        try:
            chrome_options = Options()
            
            # Basic options
            if settings.headless_mode:
                chrome_options.add_argument("--headless")
            
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # Download preferences
            prefs = {
                "download.default_directory": self.download_dir,
                "download.prompt_for_download": False,
                "download.directory_upgrade": True,
                "safebrowsing.enabled": True
            }
            chrome_options.add_experimental_option("prefs", prefs)
            
            # Setup service
            if settings.chrome_driver_path:
                service = Service(settings.chrome_driver_path)
            else:
                service = Service(ChromeDriverManager().install())
            
            # Create driver
            driver = webdriver.Chrome(service=service, options=chrome_options)
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            driver.set_page_load_timeout(settings.page_load_timeout)
            
            self.driver = driver
            self.wait = WebDriverWait(driver, settings.selenium_timeout)
            
            logger.info("Chrome WebDriver setup completed")
            return driver
            
        except Exception as e:
            logger.error(f"Error setting up Chrome WebDriver: {str(e)}")
            raise
    
    def navigate_to_namecheap(self) -> bool:
        """Navigate to Namecheap bulk search page"""
        try:
            logger.info("Navigating to Namecheap bulk search page")
            self.driver.get(settings.namecheap_bulk_search_url)
            
            # Wait for page to load
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            time.sleep(2)
            
            return True
            
        except Exception as e:
            logger.error(f"Error navigating to Namecheap: {str(e)}")
            return False
    
    def handle_popup(self) -> bool:
        """Handle popup if it appears"""
        try:
            logger.info("Checking for popup")
            
            for selector in settings.popup_selectors:
                try:
                    popup_button = self.wait.until(
                        EC.element_to_be_clickable((By.XPATH, selector))
                    )
                    popup_button.click()
                    logger.info(f"Popup closed using selector: {selector}")
                    time.sleep(1)
                    return True
                except TimeoutException:
                    continue
                except Exception as e:
                    logger.warning(f"Error with popup selector {selector}: {str(e)}")
                    continue
            
            logger.info("No popup found or popup already handled")
            return True
            
        except Exception as e:
            logger.error(f"Error handling popup: {str(e)}")
            return False
    
    def input_domains(self, domains: List[str]) -> bool:
        """Input domains into the search field"""
        try:
            logger.info(f"Inputting {len(domains)} domains")
            
            # Find domain input field
            domain_input = self.wait.until(
                EC.presence_of_element_located((By.XPATH, settings.domain_input_selector))
            )
            
            # Clear existing content
            domain_input.clear()
            
            # Input domains (one per line)
            domain_text = "\n".join(domains)
            domain_input.send_keys(domain_text)
            
            logger.info("Domains input completed")
            return True
            
        except Exception as e:
            logger.error(f"Error inputting domains: {str(e)}")
            return False
    
    def configure_search_options(self) -> bool:
        """Configure search options (uncheck first, check second)"""
        try:
            logger.info("Configuring search options")
            
            # Uncheck first option
            try:
                uncheck_option = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, settings.uncheck_option_selector))
                )
                if uncheck_option.is_selected():
                    uncheck_option.click()
                    logger.info("First option unchecked")
            except Exception as e:
                logger.warning(f"Could not uncheck first option: {str(e)}")
            
            # Check second option
            try:
                check_option = self.wait.until(
                    EC.element_to_be_clickable((By.XPATH, settings.check_option_selector))
                )
                if not check_option.is_selected():
                    check_option.click()
                    logger.info("Second option checked")
            except Exception as e:
                logger.warning(f"Could not check second option: {str(e)}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error configuring search options: {str(e)}")
            return False
    
    def start_search(self) -> bool:
        """Click the search button to start domain checking"""
        try:
            logger.info("Starting domain search")
            
            search_button = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, settings.search_button_selector))
            )
            search_button.click()
            
            logger.info("Search started")
            return True
            
        except Exception as e:
            logger.error(f"Error starting search: {str(e)}")
            return False

    def wait_for_results_method1(self) -> bool:
        """Method 1: Count domain results every 2 seconds, wait for stable count"""
        try:
            logger.info("Using Method 1: Counting domain results")

            stable_count = 0
            last_count = 0
            max_cycles = settings.max_result_wait_cycles

            for cycle in range(max_cycles * 3):  # Extended cycles for method 1
                time.sleep(settings.result_check_interval)

                try:
                    # Count domain result elements
                    result_elements = self.driver.find_elements(
                        By.CSS_SELECTOR,
                        "[data-testid='domain-result'], .domain-result, .search-result-item"
                    )
                    current_count = len(result_elements)

                    logger.info(f"Cycle {cycle + 1}: Found {current_count} domain results")

                    if current_count == last_count and current_count > 0:
                        stable_count += 1
                        if stable_count >= max_cycles:
                            logger.info(f"Results stable at {current_count} domains")
                            return True
                    else:
                        stable_count = 0
                        last_count = current_count

                except Exception as e:
                    logger.warning(f"Error counting results in cycle {cycle + 1}: {str(e)}")
                    continue

            logger.warning("Method 1: Timeout waiting for stable results")
            return False

        except Exception as e:
            logger.error(f"Error in wait_for_results_method1: {str(e)}")
            return False

    def wait_for_results_method2(self) -> bool:
        """Method 2: Monitor Chrome DevTools Performance logs for idle state"""
        try:
            logger.info("Using Method 2: Monitoring performance logs")

            # Enable performance logging
            self.driver.execute_cdp_cmd('Performance.enable', {})
            self.driver.execute_cdp_cmd('Network.enable', {})

            idle_start_time = None
            idle_threshold = settings.idle_wait_time / 1000  # Convert to seconds
            max_wait_time = 300  # 5 minutes max
            start_time = time.time()

            while time.time() - start_time < max_wait_time:
                time.sleep(1)

                try:
                    # Get network activity
                    logs = self.driver.get_log('performance')
                    recent_network_activity = False

                    current_time = time.time() * 1000  # Convert to milliseconds

                    for log in logs:
                        message = json.loads(log['message'])
                        if message.get('message', {}).get('method', '').startswith('Network.'):
                            log_time = message.get('message', {}).get('params', {}).get('timestamp', 0) * 1000
                            if current_time - log_time < idle_threshold * 1000:
                                recent_network_activity = True
                                break

                    if not recent_network_activity:
                        if idle_start_time is None:
                            idle_start_time = time.time()
                        elif time.time() - idle_start_time >= idle_threshold:
                            logger.info("Network idle state detected")
                            return True
                    else:
                        idle_start_time = None

                except Exception as e:
                    logger.warning(f"Error monitoring performance logs: {str(e)}")
                    continue

            logger.warning("Method 2: Timeout waiting for idle state")
            return False

        except Exception as e:
            logger.error(f"Error in wait_for_results_method2: {str(e)}")
            return False

    def wait_for_results_method3(self) -> bool:
        """Method 3: Check and click 'Load More' button until it disappears"""
        try:
            logger.info("Using Method 3: Checking for Load More button")

            max_load_more_clicks = 50  # Prevent infinite loops
            clicks = 0

            while clicks < max_load_more_clicks:
                time.sleep(2)

                try:
                    # Look for Load More button
                    load_more_button = self.driver.find_element(
                        By.XPATH, settings.load_more_button_selector
                    )

                    if load_more_button.is_displayed() and load_more_button.is_enabled():
                        logger.info(f"Clicking Load More button (click #{clicks + 1})")

                        # Scroll to button and click
                        self.driver.execute_script("arguments[0].scrollIntoView(true);", load_more_button)
                        time.sleep(1)
                        load_more_button.click()
                        clicks += 1

                        # Wait a bit for new results to load
                        time.sleep(3)
                    else:
                        logger.info("Load More button not clickable")
                        break

                except NoSuchElementException:
                    logger.info("Load More button not found - all results loaded")
                    return True
                except ElementClickInterceptedException:
                    logger.warning("Load More button click intercepted, trying again")
                    time.sleep(2)
                    continue
                except Exception as e:
                    logger.warning(f"Error with Load More button: {str(e)}")
                    break

            logger.info(f"Completed Load More clicking after {clicks} clicks")
            return True

        except Exception as e:
            logger.error(f"Error in wait_for_results_method3: {str(e)}")
            return False

    def wait_for_results_complete(self) -> bool:
        """Wait for results to complete using all three methods"""
        try:
            logger.info("Starting comprehensive results waiting")

            # Try Method 3 first (Load More buttons)
            if self.wait_for_results_method3():
                logger.info("Method 3 completed successfully")

                # Then use Method 1 to ensure stability
                if self.wait_for_results_method1():
                    logger.info("Method 1 confirmed stability")
                    return True
                else:
                    logger.warning("Method 1 failed after Method 3")

            # Fallback to Method 2 if others fail
            logger.info("Falling back to Method 2")
            return self.wait_for_results_method2()

        except Exception as e:
            logger.error(f"Error in wait_for_results_complete: {str(e)}")
            return False

    def export_results(self, batch_id: str) -> Optional[str]:
        """Export results and return the downloaded file path"""
        try:
            logger.info("Starting results export")

            # Click export button
            export_button = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, settings.export_button_selector))
            )
            export_button.click()

            logger.info("Export button clicked")

            # Wait for download to complete
            download_file = self.wait_for_download(batch_id)

            if download_file:
                logger.info(f"Export completed: {download_file}")
                return download_file
            else:
                logger.error("Export failed - no file downloaded")
                return None

        except Exception as e:
            logger.error(f"Error exporting results: {str(e)}")
            return None

    def wait_for_download(self, batch_id: str, timeout: int = 60) -> Optional[str]:
        """Wait for file download to complete"""
        try:
            start_time = time.time()

            while time.time() - start_time < timeout:
                # Check for new files in download directory
                files = os.listdir(self.download_dir)
                csv_files = [f for f in files if f.endswith('.csv') and not f.endswith('.crdownload')]

                if csv_files:
                    # Get the most recent CSV file
                    latest_file = max(
                        [os.path.join(self.download_dir, f) for f in csv_files],
                        key=os.path.getctime
                    )

                    # Rename file with batch ID
                    timestamp = int(time.time())
                    new_filename = f"namecheap_results_{batch_id}_{timestamp}.csv"
                    new_filepath = os.path.join(self.download_dir, new_filename)

                    os.rename(latest_file, new_filepath)
                    logger.info(f"Downloaded file renamed to: {new_filename}")

                    return new_filepath

                time.sleep(1)

            logger.error("Download timeout")
            return None

        except Exception as e:
            logger.error(f"Error waiting for download: {str(e)}")
            return None

    def cleanup(self):
        """Clean up WebDriver resources"""
        try:
            if self.driver:
                self.driver.quit()
                logger.info("WebDriver cleaned up")
        except Exception as e:
            logger.error(f"Error cleaning up WebDriver: {str(e)}")

    def process_batch(self, domains: List[str], batch_id: str) -> Tuple[bool, Optional[str]]:
        """Process a single batch of domains"""
        try:
            logger.info(f"Processing batch {batch_id} with {len(domains)} domains")

            # Setup driver
            self.setup_driver()

            # Navigate to Namecheap
            if not self.navigate_to_namecheap():
                return False, None

            # Handle popup
            if not self.handle_popup():
                return False, None

            # Input domains
            if not self.input_domains(domains):
                return False, None

            # Configure options
            if not self.configure_search_options():
                return False, None

            # Start search
            if not self.start_search():
                return False, None

            # Wait for results
            if not self.wait_for_results_complete():
                return False, None

            # Export results
            file_path = self.export_results(batch_id)

            return file_path is not None, file_path

        except Exception as e:
            logger.error(f"Error processing batch {batch_id}: {str(e)}")
            return False, None
        finally:
            self.cleanup()
