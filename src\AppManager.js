/**
 * App Manager - Main application controller
 * Integrates all components and manages application state
 */
import { DomainValidator } from '../utils/DomainValidator.js';
import { DateFormatter } from '../utils/DateFormatter.js';
import { ExportHelper } from '../utils/ExportHelper.js';
import { WaybackAPI } from '../services/WaybackAPI.js';
import { RateLimiter } from '../services/RateLimiter.js';
import { DataProcessor } from '../services/DataProcessor.js';
import { DomainInput } from './DomainInput.js';
import { ProgressBar } from './ProgressBar.js';
import { ResultsTable } from './ResultsTable.js';
import { ExportButtons } from './ExportButtons.js';

export class AppManager {
    constructor() {
        // Initialize state
        this.state = {
            domains: [],
            results: [],
            isProcessing: false,
            isPaused: false,
            currentDomain: '',
            processedCount: 0,
            totalCount: 0,
            errors: [],
            startTime: null,
            endTime: null
        };

        // Initialize services
        this.rateLimiter = new RateLimiter({
            requestsPerSecond: 0.8,
            enableBurstControl: true,
            enableCircuitBreaker: true
        });

        this.waybackAPI = new WaybackAPI({
            rateLimiter: this.rateLimiter,
            enableCaching: true,
            cacheTimeout: 300000 // 5 minutes
        });

        this.dataProcessor = new DataProcessor({
            batchSize: 10,
            maxConcurrent: 5,
            enableRetry: true,
            maxRetries: 3
        });

        // Initialize utilities
        this.domainValidator = new DomainValidator();
        this.dateFormatter = new DateFormatter();
        this.exportHelper = new ExportHelper();

        // Initialize components
        this.components = {};
        
        // Bind methods
        this.init = this.init.bind(this);
        this.startProcessing = this.startProcessing.bind(this);
        this.pauseProcessing = this.pauseProcessing.bind(this);
        this.resumeProcessing = this.resumeProcessing.bind(this);
        this.stopProcessing = this.stopProcessing.bind(this);
        this.handleError = this.handleError.bind(this);
        this.updateProgress = this.updateProgress.bind(this);

        // Auto-initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', this.init);
        } else {
            this.init();
        }
    }

    /**
     * Initializes the application
     */
    init() {
        try {
            this.initializeComponents();
            this.setupEventListeners();
            this.restoreState();
            this.updateUI();
            
            console.log('Wayback Machine Snapshot Checker initialized successfully');
        } catch (error) {
            console.error('Failed to initialize application:', error);
            this.showError('Failed to initialize application. Please refresh the page.');
        }
    }

    /**
     * Initializes all components
     */
    initializeComponents() {
        // Initialize Domain Input component
        this.components.domainInput = new DomainInput({
            maxDomains: 5000,
            enableFileUpload: true,
            enableValidation: true,
            supportedFormats: ['txt', 'csv']
        });

        // Initialize Progress Bar component
        this.components.progressBar = new ProgressBar({
            showETA: true,
            showSpeed: true,
            showStats: true,
            enablePauseResume: true
        });

        // Initialize Results Table component
        this.components.resultsTable = new ResultsTable({
            enableSorting: true,
            enableFiltering: true,
            enableRowHighlight: true,
            enableInlineEdit: false,
            pageSize: 50
        });

        // Initialize Export Buttons component
        this.components.exportButtons = new ExportButtons({
            enabledFormats: ['csv', 'tsv', 'json'],
            showFileSize: true,
            showDownloadCount: true
        });

        // Set up component callbacks
        this.setupComponentCallbacks();
    }

    /**
     * Sets up component callbacks
     */
    setupComponentCallbacks() {
        // Domain Input callbacks
        this.components.domainInput.onDomainsChange = (domains) => {
            this.handleDomainsChange(domains);
        };

        this.components.domainInput.onValidationError = (error) => {
            this.showError(`Validation error: ${error}`);
        };

        this.components.domainInput.onFileUpload = (data) => {
            this.handleFileUpload(data);
        };

        // Progress Bar callbacks
        this.components.progressBar.onPause = () => {
            this.pauseProcessing();
        };

        this.components.progressBar.onResume = () => {
            this.resumeProcessing();
        };

        this.components.progressBar.onStop = () => {
            this.stopProcessing();
        };

        // Results Table callbacks
        this.components.resultsTable.onRowClick = (result) => {
            this.handleResultClick(result);
        };

        this.components.resultsTable.onFilter = (filters) => {
            this.handleResultsFilter(filters);
        };

        this.components.resultsTable.onSort = (sortConfig) => {
            this.handleResultsSort(sortConfig);
        };

        // Export Buttons callbacks
        this.components.exportButtons.onExport = (exportInfo) => {
            this.handleExport(exportInfo);
        };

        this.components.exportButtons.onError = (error) => {
            this.handleError(error);
        };

        // Data Processor callbacks
        this.dataProcessor.onProgress = (progress) => {
            this.updateProgress(progress);
        };

        this.dataProcessor.onResult = (result) => {
            this.handleResult(result);
        };

        this.dataProcessor.onError = (error) => {
            this.handleError(error);
        };

        this.dataProcessor.onComplete = (summary) => {
            this.handleProcessingComplete(summary);
        };
    }

    /**
     * Sets up global event listeners
     */
    setupEventListeners() {
        // Global start button
        const startBtn = document.getElementById('start-btn');
        if (startBtn) {
            startBtn.addEventListener('click', () => {
                this.startProcessing();
            });
        }

        // Global pause button
        const pauseBtn = document.getElementById('pause-btn');
        if (pauseBtn) {
            pauseBtn.addEventListener('click', () => {
                this.pauseProcessing();
            });
        }

        // Global stop button
        const stopBtn = document.getElementById('stop-btn');
        if (stopBtn) {
            stopBtn.addEventListener('click', () => {
                this.stopProcessing();
            });
        }

        // Global clear button
        const clearBtn = document.getElementById('clear-btn');
        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearResults();
            });
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // Window events
        window.addEventListener('beforeunload', (e) => {
            this.handleBeforeUnload(e);
        });

        // Visibility change (tab switching)
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });
    }

    /**
     * Handles domains change from input component
     * @param {Array} domains - New domains list
     */
    handleDomainsChange(domains) {
        this.state.domains = [...domains];
        this.updateStartButtonState();
        this.saveState();
    }

    /**
     * Handles file upload from input component
     * @param {Object} data - Upload data
     */
    handleFileUpload(data) {
        const { domains, format, filename } = data;
        
        this.showSuccess(`Loaded ${domains.length} domains from ${filename}`);
        this.state.domains = domains;
        this.components.domainInput.setDomains(domains);
        this.updateStartButtonState();
        this.saveState();
    }

    /**
     * Starts domain processing
     */
    async startProcessing() {
        if (this.state.isProcessing) {
            this.showWarning('Processing is already in progress');
            return;
        }

        if (this.state.domains.length === 0) {
            this.showError('Please enter domains to process');
            return;
        }

        try {
            // Validate domains before processing
            const validationResult = this.domainValidator.validateBatch(this.state.domains);
            
            if (validationResult.valid.length === 0) {
                this.showError('No valid domains to process');
                return;
            }

            if (validationResult.invalid.length > 0) {
                const message = `Found ${validationResult.invalid.length} invalid domains. Continue with ${validationResult.valid.length} valid domains?`;
                if (!confirm(message)) {
                    return;
                }
            }

            // Initialize processing state
            this.state.isProcessing = true;
            this.state.isPaused = false;
            this.state.currentDomain = '';
            this.state.processedCount = 0;
            this.state.totalCount = validationResult.valid.length;
            this.state.errors = [];
            this.state.startTime = Date.now();
            this.state.endTime = null;
            this.state.results = [];

            // Update UI
            this.updateUI();
            this.components.progressBar.start(this.state.totalCount);
            this.components.resultsTable.clear();

            // Start processing
            await this.dataProcessor.processBatch(
                validationResult.valid,
                (domain) => this.processSingleDomain(domain)
            );

        } catch (error) {
            this.handleError(error);
            this.stopProcessing();
        }
    }

    /**
     * Processes a single domain
     * @param {string} domain - Domain to process
     * @returns {Promise<Object>} Processing result
     */
    async processSingleDomain(domain) {
        try {
            this.state.currentDomain = domain;
            
            // Get snapshots from Wayback Machine
            const snapshots = await this.waybackAPI.getSnapshots(domain);
            
            // Process snapshot data
            const result = this.processSnapshotData(domain, snapshots);
            
            return result;
            
        } catch (error) {
            console.error(`Error processing domain ${domain}:`, error);
            
            return {
                domain,
                error: error.message,
                snapshotCount: 0,
                firstSnapshot: null,
                lastSnapshot: null,
                statusCodes: {},
                totalSize: 0,
                responseTime: 0
            };
        }
    }

    /**
     * Processes snapshot data
     * @param {string} domain - Domain name
     * @param {Array} snapshots - Raw snapshot data
     * @returns {Object} Processed result
     */
    processSnapshotData(domain, snapshots) {
        if (!snapshots || snapshots.length === 0) {
            return {
                domain,
                error: 'No snapshots found',
                snapshotCount: 0,
                firstSnapshot: null,
                lastSnapshot: null,
                statusCodes: {},
                totalSize: 0,
                responseTime: 0
            };
        }

        // Sort snapshots by timestamp
        const sortedSnapshots = snapshots.sort((a, b) => a.timestamp.localeCompare(b.timestamp));
        
        // Calculate statistics
        const statusCodes = {};
        let totalSize = 0;
        
        snapshots.forEach(snapshot => {
            const statusCode = snapshot.statuscode || 'unknown';
            statusCodes[statusCode] = (statusCodes[statusCode] || 0) + 1;
            
            if (snapshot.length && !isNaN(parseInt(snapshot.length))) {
                totalSize += parseInt(snapshot.length);
            }
        });

        // Format dates
        const firstSnapshot = this.dateFormatter.formatWaybackTimestamp(
            sortedSnapshots[0].timestamp
        );
        const lastSnapshot = this.dateFormatter.formatWaybackTimestamp(
            sortedSnapshots[sortedSnapshots.length - 1].timestamp
        );

        return {
            domain,
            error: null,
            snapshotCount: snapshots.length,
            firstSnapshot,
            lastSnapshot,
            statusCodes,
            totalSize,
            responseTime: 0, // Will be set by rate limiter
            rawSnapshots: snapshots.slice(0, 10) // Keep first 10 for details
        };
    }

    /**
     * Updates processing progress
     * @param {Object} progress - Progress information
     */
    updateProgress(progress) {
        this.state.processedCount = progress.processed;
        this.state.currentDomain = progress.current || '';
        
        this.components.progressBar.updateProgress(
            progress.processed,
            this.state.totalCount,
            {
                current: progress.current,
                speed: progress.speed,
                eta: progress.eta,
                errors: this.state.errors.length
            }
        );

        this.updateUI();
    }

    /**
     * Handles single domain result
     * @param {Object} result - Domain processing result
     */
    handleResult(result) {
        this.state.results.push(result);
        
        if (result.error) {
            this.state.errors.push({
                domain: result.domain,
                error: result.error,
                timestamp: Date.now()
            });
        }

        // Update results table
        this.components.resultsTable.addResult(result);
        
        // Update export buttons
        this.components.exportButtons.setData(this.state.results);
        
        // Save state
        this.saveState();
    }

    /**
     * Handles processing completion
     * @param {Object} summary - Processing summary
     */
    handleProcessingComplete(summary) {
        this.state.isProcessing = false;
        this.state.isPaused = false;
        this.state.endTime = Date.now();
        this.state.currentDomain = '';

        // Update progress bar
        this.components.progressBar.complete({
            total: summary.total,
            successful: summary.successful,
            errors: summary.errors,
            duration: this.state.endTime - this.state.startTime
        });

        // Show completion message
        const duration = Math.round((this.state.endTime - this.state.startTime) / 1000);
        const message = `Processing completed! ${summary.successful}/${summary.total} domains processed in ${duration}s`;
        
        if (summary.errors > 0) {
            this.showWarning(`${message} (${summary.errors} errors)`);
        } else {
            this.showSuccess(message);
        }

        this.updateUI();
        this.saveState();
    }

    /**
     * Pauses processing
     */
    pauseProcessing() {
        if (!this.state.isProcessing || this.state.isPaused) {
            return;
        }

        this.state.isPaused = true;
        this.dataProcessor.pause();
        this.components.progressBar.pause();
        
        this.updateUI();
        this.showInfo('Processing paused');
    }

    /**
     * Resumes processing
     */
    resumeProcessing() {
        if (!this.state.isProcessing || !this.state.isPaused) {
            return;
        }

        this.state.isPaused = false;
        this.dataProcessor.resume();
        this.components.progressBar.resume();
        
        this.updateUI();
        this.showInfo('Processing resumed');
    }

    /**
     * Stops processing
     */
    stopProcessing() {
        if (!this.state.isProcessing) {
            return;
        }

        this.state.isProcessing = false;
        this.state.isPaused = false;
        this.state.currentDomain = '';
        
        this.dataProcessor.stop();
        this.components.progressBar.stop();
        
        this.updateUI();
        this.showInfo('Processing stopped');
        this.saveState();
    }

    /**
     * Clears all results
     */
    clearResults() {
        if (this.state.isProcessing) {
            if (!confirm('Processing is in progress. Stop and clear results?')) {
                return;
            }
            this.stopProcessing();
        }

        this.state.results = [];
        this.state.errors = [];
        this.state.processedCount = 0;
        this.state.totalCount = 0;
        
        this.components.resultsTable.clear();
        this.components.progressBar.reset();
        this.components.exportButtons.setData([]);
        
        this.updateUI();
        this.saveState();
        this.showInfo('Results cleared');
    }

    /**
     * Handles result row click
     * @param {Object} result - Clicked result
     */
    handleResultClick(result) {
        // Show detailed information in a modal or expanded view
        this.showResultDetails(result);
    }

    /**
     * Shows detailed result information
     * @param {Object} result - Result to show details for
     */
    showResultDetails(result) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        
        const content = document.createElement('div');
        content.className = 'bg-white rounded-lg p-6 max-w-2xl max-h-96 overflow-y-auto';
        
        content.innerHTML = `
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">Domain Details: ${result.domain}</h3>
                <button class="text-gray-500 hover:text-gray-700" onclick="this.closest('.fixed').remove()">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <div class="space-y-4">
                <div>
                    <strong>Snapshot Count:</strong> ${result.snapshotCount || 'N/A'}
                </div>
                
                <div>
                    <strong>Time Range:</strong> 
                    ${result.firstSnapshot && result.lastSnapshot 
                        ? `${result.firstSnapshot} - ${result.lastSnapshot}`
                        : 'N/A'
                    }
                </div>
                
                <div>
                    <strong>Status Codes:</strong>
                    <pre class="bg-gray-100 p-2 rounded mt-1">${JSON.stringify(result.statusCodes, null, 2)}</pre>
                </div>
                
                <div>
                    <strong>Total Size:</strong> ${this.formatFileSize(result.totalSize)}
                </div>
                
                ${result.error ? `
                    <div>
                        <strong class="text-red-600">Error:</strong> 
                        <span class="text-red-600">${result.error}</span>
                    </div>
                ` : ''}
                
                ${result.rawSnapshots && result.rawSnapshots.length > 0 ? `
                    <div>
                        <strong>Recent Snapshots:</strong>
                        <div class="max-h-32 overflow-y-auto">
                            ${result.rawSnapshots.map(snapshot => `
                                <div class="text-sm py-1 border-b">
                                    ${this.dateFormatter.formatWaybackTimestamp(snapshot.timestamp)} - 
                                    Status: ${snapshot.statuscode} - 
                                    Size: ${this.formatFileSize(parseInt(snapshot.length) || 0)}
                                </div>
                            `).join('')}
                        </div>
                    </div>
                ` : ''}
            </div>
        `;
        
        modal.appendChild(content);
        document.body.appendChild(modal);
        
        // Close on background click
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    /**
     * Handles results filtering
     * @param {Object} filters - Filter configuration
     */
    handleResultsFilter(filters) {
        // Filter logic is handled by ResultsTable component
        // This is just for logging/analytics
        console.log('Results filtered:', filters);
    }

    /**
     * Handles results sorting
     * @param {Object} sortConfig - Sort configuration
     */
    handleResultsSort(sortConfig) {
        // Sort logic is handled by ResultsTable component
        // This is just for logging/analytics
        console.log('Results sorted:', sortConfig);
    }

    /**
     * Handles export events
     * @param {Object} exportInfo - Export information
     */
    handleExport(exportInfo) {
        console.log('Data exported:', exportInfo);
        this.showSuccess(`Exported ${exportInfo.recordCount} records as ${exportInfo.format.toUpperCase()}`);
    }

    /**
     * Handles keyboard shortcuts
     * @param {KeyboardEvent} event - Keyboard event
     */
    handleKeyboardShortcuts(event) {
        // Ctrl+Enter to start processing
        if (event.ctrlKey && event.key === 'Enter') {
            event.preventDefault();
            if (!this.state.isProcessing) {
                this.startProcessing();
            }
        }

        // Space to pause/resume
        if (event.code === 'Space' && !['INPUT', 'TEXTAREA'].includes(document.activeElement.tagName)) {
            event.preventDefault();
            if (this.state.isProcessing) {
                if (this.state.isPaused) {
                    this.resumeProcessing();
                } else {
                    this.pauseProcessing();
                }
            }
        }

        // Escape to stop
        if (event.key === 'Escape') {
            if (this.state.isProcessing) {
                this.stopProcessing();
            }
        }

        // Ctrl+L to clear
        if (event.ctrlKey && event.key === 'l') {
            event.preventDefault();
            this.clearResults();
        }
    }

    /**
     * Handles before page unload
     * @param {BeforeUnloadEvent} event - Before unload event
     */
    handleBeforeUnload(event) {
        if (this.state.isProcessing) {
            event.preventDefault();
            event.returnValue = 'Processing is in progress. Are you sure you want to leave?';
            return event.returnValue;
        }
    }

    /**
     * Handles visibility change (tab switching)
     */
    handleVisibilityChange() {
        if (document.hidden && this.state.isProcessing && !this.state.isPaused) {
            // Consider pausing when tab is hidden to save resources
            // this.pauseProcessing();
        } else if (!document.hidden && this.state.isProcessing && this.state.isPaused) {
            // Consider auto-resuming when tab becomes visible
            // this.resumeProcessing();
        }
    }

    /**
     * Updates start button state
     */
    updateStartButtonState() {
        const startBtn = document.getElementById('start-btn');
        if (startBtn) {
            const hasValidDomains = this.state.domains.length > 0;
            const canStart = hasValidDomains && !this.state.isProcessing;
            
            startBtn.disabled = !canStart;
            
            if (canStart) {
                startBtn.classList.remove('opacity-50', 'cursor-not-allowed');
                startBtn.textContent = `Check ${this.state.domains.length} domains`;
            } else {
                startBtn.classList.add('opacity-50', 'cursor-not-allowed');
                startBtn.textContent = this.state.isProcessing ? 'Processing...' : 'Enter domains to start';
            }
        }
    }

    /**
     * Updates the entire UI
     */
    updateUI() {
        this.updateStartButtonState();
        this.updateControlButtons();
        this.updateStatusDisplay();
    }

    /**
     * Updates control buttons (pause, stop, clear)
     */
    updateControlButtons() {
        const pauseBtn = document.getElementById('pause-btn');
        const stopBtn = document.getElementById('stop-btn');
        const clearBtn = document.getElementById('clear-btn');

        if (pauseBtn) {
            pauseBtn.disabled = !this.state.isProcessing;
            pauseBtn.textContent = this.state.isPaused ? 'Resume' : 'Pause';
            
            if (this.state.isProcessing) {
                pauseBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            } else {
                pauseBtn.classList.add('opacity-50', 'cursor-not-allowed');
            }
        }

        if (stopBtn) {
            stopBtn.disabled = !this.state.isProcessing;
            
            if (this.state.isProcessing) {
                stopBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            } else {
                stopBtn.classList.add('opacity-50', 'cursor-not-allowed');
            }
        }

        if (clearBtn) {
            const hasResults = this.state.results.length > 0;
            clearBtn.disabled = !hasResults;
            
            if (hasResults) {
                clearBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            } else {
                clearBtn.classList.add('opacity-50', 'cursor-not-allowed');
            }
        }
    }

    /**
     * Updates status display
     */
    updateStatusDisplay() {
        const statusElement = document.getElementById('status-display');
        if (statusElement) {
            let statusText = '';
            
            if (this.state.isProcessing) {
                if (this.state.isPaused) {
                    statusText = `Paused at ${this.state.processedCount}/${this.state.totalCount}`;
                } else {
                    statusText = `Processing: ${this.state.processedCount}/${this.state.totalCount}`;
                    if (this.state.currentDomain) {
                        statusText += ` - Current: ${this.state.currentDomain}`;
                    }
                }
            } else if (this.state.results.length > 0) {
                const successCount = this.state.results.filter(r => !r.error).length;
                const errorCount = this.state.results.filter(r => r.error).length;
                statusText = `Completed: ${successCount} successful, ${errorCount} errors`;
            } else {
                statusText = 'Ready to process domains';
            }
            
            statusElement.textContent = statusText;
        }
    }

    /**
     * Saves application state to localStorage
     */
    saveState() {
        try {
            const stateToSave = {
                domains: this.state.domains,
                results: this.state.results,
                errors: this.state.errors,
                processedCount: this.state.processedCount,
                totalCount: this.state.totalCount,
                timestamp: Date.now()
            };
            
            localStorage.setItem('wayback-checker-state', JSON.stringify(stateToSave));
        } catch (error) {
            console.warn('Failed to save state:', error);
        }
    }

    /**
     * Restores application state from localStorage
     */
    restoreState() {
        try {
            const savedState = localStorage.getItem('wayback-checker-state');
            if (savedState) {
                const state = JSON.parse(savedState);
                
                // Only restore if not too old (24 hours)
                const maxAge = 24 * 60 * 60 * 1000;
                if (Date.now() - state.timestamp < maxAge) {
                    this.state.domains = state.domains || [];
                    this.state.results = state.results || [];
                    this.state.errors = state.errors || [];
                    
                    // Update components
                    if (this.state.domains.length > 0) {
                        this.components.domainInput.setDomains(this.state.domains);
                    }
                    
                    if (this.state.results.length > 0) {
                        this.components.resultsTable.setResults(this.state.results);
                        this.components.exportButtons.setData(this.state.results);
                    }
                }
            }
        } catch (error) {
            console.warn('Failed to restore state:', error);
        }
    }

    /**
     * Handles errors
     * @param {Error|string} error - Error to handle
     */
    handleError(error) {
        const message = error instanceof Error ? error.message : String(error);
        console.error('Application error:', error);
        this.showError(message);
    }

    /**
     * Formats file size for display
     * @param {number} bytes - Size in bytes
     * @returns {string} Formatted size
     */
    formatFileSize(bytes) {
        if (!bytes || isNaN(bytes)) return 'N/A';
        
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
    }

    /**
     * Shows success toast
     * @param {string} message - Success message
     */
    showSuccess(message) {
        this.showToast(message, 'success');
    }

    /**
     * Shows info toast
     * @param {string} message - Info message
     */
    showInfo(message) {
        this.showToast(message, 'info');
    }

    /**
     * Shows warning toast
     * @param {string} message - Warning message
     */
    showWarning(message) {
        this.showToast(message, 'warning');
    }

    /**
     * Shows error toast
     * @param {string} message - Error message
     */
    showError(message) {
        this.showToast(message, 'error');
    }

    /**
     * Shows toast notification
     * @param {string} message - Message to show
     * @param {string} type - Toast type (success, info, warning, error)
     */
    showToast(message, type = 'info') {
        // Remove existing toasts
        document.querySelectorAll('.toast').forEach(toast => toast.remove());
        
        const toast = document.createElement('div');
        toast.className = `toast fixed top-4 right-4 p-4 rounded-md shadow-lg text-white transform translate-x-full transition-transform duration-300 z-50 max-w-sm ${
            type === 'error' ? 'bg-red-600' : 
            type === 'success' ? 'bg-green-600' : 
            type === 'warning' ? 'bg-yellow-600' : 'bg-blue-600'
        }`;
        
        toast.innerHTML = `
            <div class="flex items-center">
                <span class="flex-1">${message}</span>
                <button class="ml-2 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        `;
        
        document.body.appendChild(toast);
        
        // Animate in
        setTimeout(() => {
            toast.classList.remove('translate-x-full');
        }, 100);
        
        // Auto-remove after delay
        const delay = type === 'error' ? 8000 : type === 'warning' ? 6000 : 4000;
        setTimeout(() => {
            if (toast.parentNode) {
                toast.classList.add('translate-x-full');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }
        }, delay);
    }

    /**
     * Gets application statistics
     * @returns {Object} Application stats
     */
    getStats() {
        return {
            domainsCount: this.state.domains.length,
            resultsCount: this.state.results.length,
            successCount: this.state.results.filter(r => !r.error).length,
            errorCount: this.state.results.filter(r => r.error).length,
            processingTime: this.state.endTime && this.state.startTime 
                ? this.state.endTime - this.state.startTime 
                : null,
            isProcessing: this.state.isProcessing,
            isPaused: this.state.isPaused,
            rateLimiterStats: this.rateLimiter.getStats(),
            exportStats: this.components.exportButtons.getStats()
        };
    }

    /**
     * Resets the entire application
     */
    reset() {
        this.stopProcessing();
        this.clearResults();
        this.components.domainInput.clear();
        
        this.state = {
            domains: [],
            results: [],
            isProcessing: false,
            isPaused: false,
            currentDomain: '',
            processedCount: 0,
            totalCount: 0,
            errors: [],
            startTime: null,
            endTime: null
        };

        this.rateLimiter.reset();
        this.components.exportButtons.resetStats();
        
        localStorage.removeItem('wayback-checker-state');
        this.updateUI();
        this.showInfo('Application reset');
    }
}

// Initialize the application
const app = new AppManager();
