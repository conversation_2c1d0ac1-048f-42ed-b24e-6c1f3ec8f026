/**
 * Export Buttons Component
 * Handles export functionality with multiple formats
 */
export class ExportButtons {
    constructor(options = {}) {
        this.config = {
            enabledFormats: ['csv', 'tsv', 'json'],
            showFileSize: true,
            showDownloadCount: false,
            ...options
        };

        // State
        this.data = [];
        this.downloadCount = 0;
        this.lastExportTime = null;

        // DOM elements
        this.elements = {};

        // Callbacks
        this.onExport = null;
        this.onError = null;

        this.init();
    }

    /**
     * Initializes the component
     */
    init() {
        this.bindElements();
        this.attachEventListeners();
        this.updateButtonStates();
    }

    /**
     * Binds DOM elements
     */
    bindElements() {
        this.elements = {
            copyBtn: document.getElementById('copy-btn'),
            exportCsvBtn: document.getElementById('export-csv-btn'),
            exportExcelBtn: document.getElementById('export-excel-btn'),
            exportJsonBtn: document.getElementById('export-json-btn'),
            exportTsvBtn: document.getElementById('export-tsv-btn'),
            container: document.getElementById('export-buttons')
        };
    }

    /**
     * Attaches event listeners
     */
    attachEventListeners() {
        // Copy to clipboard
        if (this.elements.copyBtn) {
            this.elements.copyBtn.addEventListener('click', () => {
                this.copyToClipboard();
            });
        }

        // CSV export
        if (this.elements.exportCsvBtn) {
            this.elements.exportCsvBtn.addEventListener('click', () => {
                this.exportData('csv');
            });
        }

        // TSV export
        if (this.elements.exportTsvBtn) {
            this.elements.exportTsvBtn.addEventListener('click', () => {
                this.exportData('tsv');
            });
        }

        // JSON export
        if (this.elements.exportJsonBtn) {
            this.elements.exportJsonBtn.addEventListener('click', () => {
                this.exportData('json');
            });
        }

        // Excel export (placeholder)
        if (this.elements.exportExcelBtn) {
            this.elements.exportExcelBtn.addEventListener('click', () => {
                this.exportExcel();
            });
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }

    /**
     * Sets data for export
     * @param {Array} data - Data to export
     */
    setData(data) {
        this.data = Array.isArray(data) ? [...data] : [];
        this.updateButtonStates();
    }

    /**
     * Copies data to clipboard as TSV
     */
    async copyToClipboard() {
        if (this.data.length === 0) {
            this.showError('No data to copy');
            return;
        }

        try {
            const tsvContent = this.formatDataForExport('tsv');
            const success = await this.performClipboardCopy(tsvContent);
            
            if (success) {
                this.showSuccess('Copied to clipboard');
                this.trackExport('clipboard');
            } else {
                this.showError('Failed to copy to clipboard');
            }
        } catch (error) {
            this.showError(`Copy failed: ${error.message}`);
        }
    }

    /**
     * Performs clipboard copy operation
     * @param {string} content - Content to copy
     * @returns {Promise<boolean>} Success status
     */
    async performClipboardCopy(content) {
        try {
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(content);
                return true;
            } else {
                return this.fallbackClipboardCopy(content);
            }
        } catch (error) {
            console.error('Clipboard copy failed:', error);
            return false;
        }
    }

    /**
     * Fallback clipboard copy for older browsers
     * @param {string} content - Content to copy
     * @returns {boolean} Success status
     */
    fallbackClipboardCopy(content) {
        try {
            const textArea = document.createElement('textarea');
            textArea.value = content;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            const result = document.execCommand('copy');
            document.body.removeChild(textArea);
            
            return result;
        } catch (error) {
            console.error('Fallback clipboard copy failed:', error);
            return false;
        }
    }

    /**
     * Exports data in specified format
     * @param {string} format - Export format (csv, tsv, json)
     */
    exportData(format) {
        if (this.data.length === 0) {
            this.showError('No data to export');
            return;
        }

        if (!this.config.enabledFormats.includes(format)) {
            this.showError(`Export format '${format}' is not enabled`);
            return;
        }

        try {
            const content = this.formatDataForExport(format);
            const filename = this.generateFilename(format);
            const mimeType = this.getMimeType(format);
            
            this.downloadFile(content, filename, mimeType);
            this.showSuccess(`Exported as ${format.toUpperCase()}`);
            this.trackExport(format);
            
        } catch (error) {
            this.showError(`Export failed: ${error.message}`);
        }
    }

    /**
     * Formats data for export
     * @param {string} format - Export format
     * @returns {string} Formatted content
     */
    formatDataForExport(format) {
        const headers = ['STT', 'Domain', 'Snapshot', 'Thời gian', 'StatusCode'];
        const rows = this.data.map((result, index) => [
            index + 1,
            result.domain,
            result.error ? 'Error' : result.snapshotCount,
            this.formatTimeRange(result),
            this.formatStatusCodes(result.statusCodes, result.error)
        ]);

        const allData = [headers, ...rows];

        switch (format.toLowerCase()) {
            case 'csv':
                return this.formatAsCSV(allData);
            case 'tsv':
                return this.formatAsTSV(allData);
            case 'json':
                return this.formatAsJSON();
            default:
                throw new Error(`Unsupported format: ${format}`);
        }
    }

    /**
     * Formats data as CSV
     * @param {Array} data - Data to format
     * @returns {string} CSV content
     */
    formatAsCSV(data) {
        return data.map(row => 
            row.map(cell => {
                const cellStr = String(cell);
                // Escape quotes and wrap in quotes if contains comma, quote, or newline
                if (cellStr.includes(',') || cellStr.includes('"') || cellStr.includes('\n')) {
                    return `"${cellStr.replace(/"/g, '""')}"`;
                }
                return cellStr;
            }).join(',')
        ).join('\n');
    }

    /**
     * Formats data as TSV
     * @param {Array} data - Data to format
     * @returns {string} TSV content
     */
    formatAsTSV(data) {
        return data.map(row => 
            row.map(cell => String(cell).replace(/\t/g, ' ')).join('\t')
        ).join('\n');
    }

    /**
     * Formats data as JSON
     * @returns {string} JSON content
     */
    formatAsJSON() {
        const jsonData = this.data.map((result, index) => ({
            stt: index + 1,
            domain: result.domain,
            snapshotCount: result.error ? null : result.snapshotCount,
            firstSnapshot: result.firstSnapshot || null,
            lastSnapshot: result.lastSnapshot || null,
            timeRange: this.formatTimeRange(result),
            statusCodes: result.statusCodes || {},
            error: result.error || null,
            totalSize: result.totalSize || 0,
            responseTime: result.responseTime || 0
        }));

        return JSON.stringify({
            exportDate: new Date().toISOString(),
            totalResults: jsonData.length,
            successCount: jsonData.filter(r => !r.error).length,
            errorCount: jsonData.filter(r => r.error).length,
            results: jsonData
        }, null, 2);
    }

    /**
     * Formats time range for export
     * @param {Object} result - Result data
     * @returns {string} Formatted time range
     */
    formatTimeRange(result) {
        if (result.error || !result.firstSnapshot || !result.lastSnapshot) {
            return 'N/A';
        }

        if (result.firstSnapshot === result.lastSnapshot) {
            return result.firstSnapshot;
        }

        return `${result.firstSnapshot} - ${result.lastSnapshot}`;
    }

    /**
     * Formats status codes for export
     * @param {Object} statusCodes - Status code counts
     * @param {string} error - Error message
     * @returns {string} Formatted status codes
     */
    formatStatusCodes(statusCodes, error) {
        if (error) {
            return error;
        }

        if (!statusCodes || Object.keys(statusCodes).length === 0) {
            return 'N/A';
        }

        return Object.entries(statusCodes)
            .sort(([a], [b]) => a.localeCompare(b))
            .map(([code, count]) => `${code}:${count}`)
            .join(' - ');
    }

    /**
     * Exports to Excel format (placeholder)
     */
    exportExcel() {
        this.showWarning('Excel export is not supported in Cloudflare Workers environment. Use CSV instead.');
    }

    /**
     * Downloads file
     * @param {string} content - File content
     * @param {string} filename - File name
     * @param {string} mimeType - MIME type
     */
    downloadFile(content, filename, mimeType) {
        try {
            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            link.style.display = 'none';
            
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // Clean up blob URL
            setTimeout(() => URL.revokeObjectURL(url), 100);
            
        } catch (error) {
            throw new Error(`Download failed: ${error.message}`);
        }
    }

    /**
     * Generates filename for export
     * @param {string} format - File format
     * @returns {string} Generated filename
     */
    generateFilename(format) {
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:.]/g, '-');
        const baseName = `wayback-results-${timestamp}`;
        
        return `${baseName}.${format}`;
    }

    /**
     * Gets MIME type for format
     * @param {string} format - File format
     * @returns {string} MIME type
     */
    getMimeType(format) {
        const mimeTypes = {
            csv: 'text/csv;charset=utf-8',
            tsv: 'text/tab-separated-values;charset=utf-8',
            json: 'application/json;charset=utf-8',
            txt: 'text/plain;charset=utf-8'
        };

        return mimeTypes[format] || 'text/plain;charset=utf-8';
    }

    /**
     * Updates button states based on data availability
     */
    updateButtonStates() {
        const hasData = this.data.length > 0;
        
        Object.values(this.elements).forEach(button => {
            if (button && button.tagName === 'BUTTON') {
                button.disabled = !hasData;
                
                if (hasData) {
                    button.classList.remove('opacity-50', 'cursor-not-allowed');
                } else {
                    button.classList.add('opacity-50', 'cursor-not-allowed');
                }
            }
        });

        // Update button text with file size info
        if (this.config.showFileSize && hasData) {
            this.updateButtonsWithFileSize();
        }
    }

    /**
     * Updates buttons with file size information
     */
    updateButtonsWithFileSize() {
        const estimateSizes = {
            csv: this.estimateFileSize(this.formatDataForExport('csv')),
            json: this.estimateFileSize(this.formatDataForExport('json'))
        };

        // Update CSV button
        if (this.elements.exportCsvBtn && estimateSizes.csv) {
            const originalText = this.elements.exportCsvBtn.textContent.split(' (')[0];
            this.elements.exportCsvBtn.textContent = `${originalText} (${estimateSizes.csv})`;
        }

        // Update JSON button
        if (this.elements.exportJsonBtn && estimateSizes.json) {
            const originalText = this.elements.exportJsonBtn.textContent.split(' (')[0];
            this.elements.exportJsonBtn.textContent = `${originalText} (${estimateSizes.json})`;
        }
    }

    /**
     * Estimates file size
     * @param {string} content - File content
     * @returns {string} Human readable file size
     */
    estimateFileSize(content) {
        const bytes = new Blob([content]).size;
        
        if (bytes < 1024) {
            return `${bytes} B`;
        } else if (bytes < 1024 * 1024) {
            return `${(bytes / 1024).toFixed(1)} KB`;
        } else {
            return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
        }
    }

    /**
     * Handles keyboard shortcuts
     * @param {KeyboardEvent} event - Keyboard event
     */
    handleKeyboardShortcuts(event) {
        // Ctrl+C for copy (when not in input/textarea)
        if (event.ctrlKey && event.key === 'c') {
            if (!['INPUT', 'TEXTAREA'].includes(document.activeElement.tagName)) {
                event.preventDefault();
                this.copyToClipboard();
            }
        }

        // Ctrl+S for save/export CSV
        if (event.ctrlKey && event.key === 's') {
            event.preventDefault();
            this.exportData('csv');
        }

        // Ctrl+Shift+S for save/export JSON
        if (event.ctrlKey && event.shiftKey && event.key === 'S') {
            event.preventDefault();
            this.exportData('json');
        }
    }

    /**
     * Tracks export events
     * @param {string} format - Export format
     */
    trackExport(format) {
        this.downloadCount++;
        this.lastExportTime = Date.now();

        // Trigger callback
        if (this.onExport) {
            this.onExport({
                format,
                recordCount: this.data.length,
                downloadCount: this.downloadCount,
                timestamp: this.lastExportTime
            });
        }

        // Update download count display
        if (this.config.showDownloadCount) {
            this.updateDownloadCountDisplay();
        }
    }

    /**
     * Updates download count display
     */
    updateDownloadCountDisplay() {
        const countElement = document.getElementById('download-count');
        if (countElement) {
            countElement.textContent = `Downloads: ${this.downloadCount}`;
        }
    }

    /**
     * Shows success message
     * @param {string} message - Success message
     */
    showSuccess(message) {
        this.showToast(message, 'success');
    }

    /**
     * Shows warning message
     * @param {string} message - Warning message
     */
    showWarning(message) {
        this.showToast(message, 'warning');
    }

    /**
     * Shows error message
     * @param {string} message - Error message
     */
    showError(message) {
        this.showToast(message, 'error');
        
        if (this.onError) {
            this.onError(new Error(message));
        }
    }

    /**
     * Shows toast notification
     * @param {string} message - Message to show
     * @param {string} type - Toast type (success, warning, error, info)
     */
    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `toast fixed top-4 right-4 p-4 rounded-md shadow-lg text-white transform translate-x-full transition-transform duration-300 z-50 ${
            type === 'error' ? 'bg-red-600' : 
            type === 'success' ? 'bg-green-600' : 
            type === 'warning' ? 'bg-yellow-600' : 'bg-blue-600'
        }`;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        // Animate in
        setTimeout(() => {
            toast.classList.remove('translate-x-full');
        }, 100);
        
        // Animate out and remove
        setTimeout(() => {
            toast.classList.add('translate-x-full');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    /**
     * Gets export statistics
     * @returns {Object} Export statistics
     */
    getStats() {
        return {
            downloadCount: this.downloadCount,
            lastExportTime: this.lastExportTime,
            dataCount: this.data.length,
            enabledFormats: this.config.enabledFormats,
            estimatedFileSizes: this.config.enabledFormats.reduce((sizes, format) => {
                try {
                    const content = this.formatDataForExport(format);
                    sizes[format] = this.estimateFileSize(content);
                } catch (error) {
                    sizes[format] = 'Error';
                }
                return sizes;
            }, {})
        };
    }

    /**
     * Resets export statistics
     */
    resetStats() {
        this.downloadCount = 0;
        this.lastExportTime = null;
        this.updateDownloadCountDisplay();
    }

    /**
     * Updates configuration
     * @param {Object} newConfig - New configuration
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        this.updateButtonStates();
    }

    /**
     * Adds custom export format
     * @param {string} format - Format name
     * @param {Function} formatter - Formatter function
     * @param {string} mimeType - MIME type
     */
    addCustomFormat(format, formatter, mimeType) {
        if (!this.customFormats) {
            this.customFormats = new Map();
        }
        
        this.customFormats.set(format, { formatter, mimeType });
        
        if (!this.config.enabledFormats.includes(format)) {
            this.config.enabledFormats.push(format);
        }
    }

    /**
     * Removes custom export format
     * @param {string} format - Format name
     */
    removeCustomFormat(format) {
        if (this.customFormats) {
            this.customFormats.delete(format);
        }
        
        this.config.enabledFormats = this.config.enabledFormats.filter(f => f !== format);
    }
}
