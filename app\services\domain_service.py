"""
Domain checking service - orchestrates the domain checking process
"""
import asyncio
import uuid
from typing import List, Dict, Optional
from datetime import datetime

from app.services.selenium_service import NamecheapAutomation
from app.services.csv_service import CSVProcessor
from app.models.domain_models import JobStatus, BatchInfo
from app.config import settings
from app.utils.logger import get_logger

logger = get_logger(__name__)

class DomainCheckService:
    """Service for orchestrating domain checking operations"""
    
    def __init__(self):
        self.csv_processor = CSVProcessor()
    
    def split_domains_into_batches(self, domains: List[str]) -> List[List[str]]:
        """Split domains into batches based on max_domains_per_batch setting"""
        batch_size = settings.max_domains_per_batch
        batches = []
        
        for i in range(0, len(domains), batch_size):
            batch = domains[i:i + batch_size]
            batches.append(batch)
        
        logger.info(f"Split {len(domains)} domains into {len(batches)} batches")
        return batches
    
    async def process_domains(self, job_id: str, domains: List[str], job_manager):
        """Process all domains for a job"""
        try:
            logger.info(f"Starting domain processing for job {job_id}")
            
            # Split into batches
            batches = self.split_domains_into_batches(domains)
            batch_files = []
            
            for batch_index, batch_domains in enumerate(batches):
                batch_id = f"{job_id}_batch_{batch_index + 1}"
                
                logger.info(f"Processing batch {batch_index + 1}/{len(batches)}")
                
                # Create batch info
                batch_info = BatchInfo(
                    batch_id=batch_id,
                    total_domains=len(batch_domains),
                    status=JobStatus.PROCESSING,
                    start_time=datetime.now().isoformat()
                )
                
                # Update job with current batch
                job_manager.update_current_batch(job_id, batch_info)
                
                # Process batch
                success, file_path = await self.process_single_batch(
                    batch_domains, batch_id, job_manager
                )
                
                if success and file_path:
                    batch_files.append(file_path)
                    
                    # Update batch as completed
                    batch_info.status = JobStatus.COMPLETED
                    batch_info.end_time = datetime.now().isoformat()
                    batch_info.processed_domains = len(batch_domains)
                    
                    # Update job progress
                    job_manager.update_batch_completed(job_id, batch_info)
                    
                    logger.info(f"Batch {batch_index + 1} completed successfully")
                else:
                    logger.error(f"Batch {batch_index + 1} failed")
                    batch_info.status = JobStatus.FAILED
                    batch_info.error_message = "Batch processing failed"
                    job_manager.update_current_batch(job_id, batch_info)
                    
                    # Continue with next batch instead of failing entire job
                    continue
            
            # Merge all batch files
            if batch_files:
                final_file = await self.merge_batch_results(job_id, batch_files)
                job_manager.set_results_file(job_id, final_file)
                logger.info(f"All batches processed and merged for job {job_id}")
            else:
                logger.error(f"No successful batches for job {job_id}")
                raise Exception("No batches were processed successfully")
                
        except Exception as e:
            logger.error(f"Error processing domains for job {job_id}: {str(e)}")
            raise
    
    async def process_single_batch(self, domains: List[str], batch_id: str, job_manager) -> tuple[bool, Optional[str]]:
        """Process a single batch of domains using Selenium"""
        try:
            logger.info(f"Processing single batch {batch_id}")
            
            # Create Selenium automation instance
            automation = NamecheapAutomation()
            
            # Process batch (this runs in a thread to avoid blocking)
            loop = asyncio.get_event_loop()
            success, file_path = await loop.run_in_executor(
                None, automation.process_batch, domains, batch_id
            )
            
            return success, file_path
            
        except Exception as e:
            logger.error(f"Error processing single batch {batch_id}: {str(e)}")
            return False, None
    
    async def merge_batch_results(self, job_id: str, batch_files: List[str]) -> str:
        """Merge multiple batch result files into one"""
        try:
            logger.info(f"Merging {len(batch_files)} batch files for job {job_id}")
            
            # Use CSV processor to merge files
            merged_file = await self.csv_processor.merge_csv_files(
                batch_files, f"final_results_{job_id}.csv"
            )
            
            logger.info(f"Merged file created: {merged_file}")
            return merged_file
            
        except Exception as e:
            logger.error(f"Error merging batch results for job {job_id}: {str(e)}")
            raise
