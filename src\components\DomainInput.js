/**
 * Domain Input Component
 * Handles domain input functionality including textarea, file upload, and validation
 */
export class DomainInput {
    constructor(options = {}) {
        this.config = {
            maxDomains: 5000,
            maxChars: 100000,
            supportedFileTypes: ['.txt', '.csv'],
            ...options
        };

        // State
        this.domains = [];
        this.validDomains = [];
        this.invalidDomains = [];
        this.fileContent = null;
        
        // Callbacks
        this.onDomainsChange = null;
        this.onValidationUpdate = null;
        this.onFileLoad = null;
        this.onError = null;

        // DOM elements (will be set after initialization)
        this.elements = {};
        
        this.init();
    }

    /**
     * Initializes the component
     */
    init() {
        this.bindElements();
        this.attachEventListeners();
        this.setupDragAndDrop();
    }

    /**
     * Binds DOM elements
     */
    bindElements() {
        this.elements = {
            textarea: document.getElementById('domain-textarea'),
            fileUpload: document.getElementById('file-upload'),
            fileUploadArea: document.querySelector('.file-upload-area'),
            domainCount: document.getElementById('domain-count'),
            charCount: document.getElementById('char-count'),
            validationStatus: document.getElementById('validation-status'),
            validCount: document.getElementById('valid-count'),
            invalidCount: document.getElementById('invalid-count'),
            clearBtn: document.getElementById('clear-btn'),
            fileInfo: document.getElementById('file-info')
        };
    }

    /**
     * Attaches event listeners
     */
    attachEventListeners() {
        // Textarea events
        if (this.elements.textarea) {
            this.elements.textarea.addEventListener('input', () => {
                this.handleTextareaChange();
            });

            this.elements.textarea.addEventListener('paste', () => {
                // Delay to allow paste content to be processed
                setTimeout(() => this.handleTextareaChange(), 100);
            });
        }

        // File upload events
        if (this.elements.fileUpload) {
            this.elements.fileUpload.addEventListener('change', (e) => {
                this.handleFileUpload(e);
            });
        }

        // Clear button
        if (this.elements.clearBtn) {
            this.elements.clearBtn.addEventListener('click', () => {
                this.clearAll();
            });
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }

    /**
     * Sets up drag and drop functionality
     */
    setupDragAndDrop() {
        if (!this.elements.fileUploadArea) return;

        const area = this.elements.fileUploadArea;

        area.addEventListener('dragover', (e) => {
            e.preventDefault();
            area.classList.add('border-primary-500', 'bg-primary-50');
        });

        area.addEventListener('dragleave', (e) => {
            e.preventDefault();
            if (!area.contains(e.relatedTarget)) {
                area.classList.remove('border-primary-500', 'bg-primary-50');
            }
        });

        area.addEventListener('drop', (e) => {
            e.preventDefault();
            area.classList.remove('border-primary-500', 'bg-primary-50');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                this.processFile(files[0]);
            }
        });
    }

    /**
     * Handles textarea content change
     */
    handleTextareaChange() {
        const text = this.elements.textarea.value;
        
        // Update character count
        this.updateCharCount(text.length);
        
        // Extract and validate domains
        this.extractDomains(text);
        this.validateDomains();
        this.updateUI();
        
        // Trigger callback
        if (this.onDomainsChange) {
            this.onDomainsChange({
                domains: this.domains,
                validDomains: this.validDomains,
                invalidDomains: this.invalidDomains
            });
        }
    }

    /**
     * Handles file upload
     * @param {Event} event - File upload event
     */
    handleFileUpload(event) {
        const file = event.target.files[0];
        if (file) {
            this.processFile(file);
        }
    }

    /**
     * Processes uploaded file
     * @param {File} file - File to process
     */
    async processFile(file) {
        try {
            // Validate file type
            if (!this.isValidFileType(file.name)) {
                throw new Error(`Unsupported file type. Supported: ${this.config.supportedFileTypes.join(', ')}`);
            }

            // Validate file size (10MB limit)
            if (file.size > 10 * 1024 * 1024) {
                throw new Error('File size too large. Maximum 10MB allowed.');
            }

            // Read file content
            const content = await this.readFileContent(file);
            this.fileContent = content;

            // Extract domains from file
            const domains = this.extractDomainsFromFileContent(content, file.name);
            
            // Update textarea
            this.elements.textarea.value = domains.join('\n');
            
            // Process domains
            this.extractDomains(this.elements.textarea.value);
            this.validateDomains();
            this.updateUI();

            // Show file info
            this.showFileInfo(file.name, domains.length);

            // Trigger callbacks
            if (this.onFileLoad) {
                this.onFileLoad({
                    fileName: file.name,
                    fileSize: file.size,
                    domainCount: domains.length
                });
            }

            if (this.onDomainsChange) {
                this.onDomainsChange({
                    domains: this.domains,
                    validDomains: this.validDomains,
                    invalidDomains: this.invalidDomains
                });
            }

        } catch (error) {
            this.handleError(error);
        }
    }

    /**
     * Reads file content
     * @param {File} file - File to read
     * @returns {Promise<string>} File content
     */
    readFileContent(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                resolve(e.target.result);
            };
            
            reader.onerror = () => {
                reject(new Error('Failed to read file'));
            };
            
            reader.readAsText(file);
        });
    }

    /**
     * Extracts domains from file content
     * @param {string} content - File content
     * @param {string} fileName - File name
     * @returns {Array<string>} Extracted domains
     */
    extractDomainsFromFileContent(content, fileName) {
        const isCSV = fileName.toLowerCase().endsWith('.csv');
        let domains = [];

        if (isCSV) {
            // Parse CSV - take first column
            const lines = content.split(/[\r\n]+/);
            
            for (const line of lines) {
                if (line.trim()) {
                    // Simple CSV parsing - handle quoted values
                    const match = line.match(/^"?([^",\r\n]+)"?/);
                    if (match) {
                        domains.push(match[1].trim());
                    }
                }
            }
        } else {
            // Parse TXT - one domain per line
            domains = content
                .split(/[\r\n]+/)
                .map(line => line.trim())
                .filter(line => line.length > 0);
        }

        return domains;
    }

    /**
     * Extracts domains from text
     * @param {string} text - Text content
     */
    extractDomains(text) {
        this.domains = text
            .split(/[\r\n]+/)
            .map(line => line.trim())
            .filter(line => line.length > 0);
    }

    /**
     * Validates domains
     */
    validateDomains() {
        this.validDomains = [];
        this.invalidDomains = [];

        const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/;

        for (const domain of this.domains) {
            const normalizedDomain = this.normalizeDomain(domain);
            
            if (this.validateSingleDomain(normalizedDomain)) {
                this.validDomains.push(normalizedDomain);
            } else {
                this.invalidDomains.push(domain);
            }
        }

        // Trigger validation callback
        if (this.onValidationUpdate) {
            this.onValidationUpdate({
                totalCount: this.domains.length,
                validCount: this.validDomains.length,
                invalidCount: this.invalidDomains.length,
                validDomains: this.validDomains,
                invalidDomains: this.invalidDomains
            });
        }
    }

    /**
     * Validates a single domain
     * @param {string} domain - Domain to validate
     * @returns {boolean} True if valid
     */
    validateSingleDomain(domain) {
        if (!domain || typeof domain !== 'string') {
            return false;
        }

        // Basic format check
        const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/;
        if (!domainRegex.test(domain)) {
            return false;
        }

        // Additional checks
        if (domain.includes('..') || domain.startsWith('-') || domain.endsWith('-')) {
            return false;
        }

        // Length check
        if (domain.length > 253) {
            return false;
        }

        // Check each label
        const labels = domain.split('.');
        for (const label of labels) {
            if (label.length === 0 || label.length > 63) {
                return false;
            }
        }

        return true;
    }

    /**
     * Normalizes domain
     * @param {string} domain - Domain to normalize
     * @returns {string} Normalized domain
     */
    normalizeDomain(domain) {
        return domain
            .trim()
            .toLowerCase()
            .replace(/^https?:\/\//, '')
            .replace(/^www\./, '')
            .replace(/\/.*$/, '')
            .replace(/:.*$/, '');
    }

    /**
     * Updates UI elements
     */
    updateUI() {
        // Update counts
        this.updateDomainCount();
        this.updateValidationStatus();
    }

    /**
     * Updates domain count display
     */
    updateDomainCount() {
        if (this.elements.domainCount) {
            this.elements.domainCount.textContent = `${this.domains.length} domain`;
        }
    }

    /**
     * Updates character count display
     * @param {number} count - Character count
     */
    updateCharCount(count) {
        if (this.elements.charCount) {
            this.elements.charCount.textContent = `${count}/${this.config.maxChars} ký tự`;
            
            // Change color if approaching limit
            if (count > this.config.maxChars * 0.9) {
                this.elements.charCount.classList.add('text-red-600');
            } else {
                this.elements.charCount.classList.remove('text-red-600');
            }
        }
    }

    /**
     * Updates validation status display
     */
    updateValidationStatus() {
        if (!this.elements.validationStatus) return;

        if (this.domains.length === 0) {
            this.elements.validationStatus.classList.add('hidden');
            return;
        }

        this.elements.validationStatus.classList.remove('hidden');

        // Update counts
        if (this.elements.validCount) {
            this.elements.validCount.textContent = `✓ ${this.validDomains.length} domain hợp lệ`;
        }

        if (this.elements.invalidCount) {
            this.elements.invalidCount.textContent = `✗ ${this.invalidDomains.length} domain không hợp lệ`;
        }

        // Update styling based on validation state
        const statusElement = this.elements.validationStatus;
        statusElement.classList.remove('bg-green-50', 'border-green-200', 'bg-yellow-50', 'border-yellow-200', 'bg-red-50', 'border-red-200');

        if (this.validDomains.length > this.config.maxDomains) {
            statusElement.classList.add('bg-red-50', 'border-red-200');
        } else if (this.invalidDomains.length > 0) {
            statusElement.classList.add('bg-yellow-50', 'border-yellow-200');
        } else if (this.validDomains.length > 0) {
            statusElement.classList.add('bg-green-50', 'border-green-200');
        }
    }

    /**
     * Shows file info
     * @param {string} fileName - File name
     * @param {number} domainCount - Number of domains loaded
     */
    showFileInfo(fileName, domainCount) {
        if (this.elements.fileInfo) {
            this.elements.fileInfo.classList.remove('hidden');
            this.elements.fileInfo.textContent = `Đã tải ${domainCount} domain từ ${fileName}`;
        }
    }

    /**
     * Handles keyboard shortcuts
     * @param {KeyboardEvent} event - Keyboard event
     */
    handleKeyboardShortcuts(event) {
        // Ctrl+A to select all in textarea
        if (event.ctrlKey && event.key === 'a' && document.activeElement === this.elements.textarea) {
            // Let default behavior happen
            return;
        }

        // Ctrl+V paste handling (already handled by paste event)
        if (event.ctrlKey && event.key === 'v' && document.activeElement === this.elements.textarea) {
            // Let default behavior happen, paste event will trigger validation
            return;
        }
    }

    /**
     * Checks if file type is valid
     * @param {string} fileName - File name
     * @returns {boolean} True if valid
     */
    isValidFileType(fileName) {
        return this.config.supportedFileTypes.some(type => 
            fileName.toLowerCase().endsWith(type)
        );
    }

    /**
     * Clears all input
     */
    clearAll() {
        // Clear textarea
        if (this.elements.textarea) {
            this.elements.textarea.value = '';
        }

        // Clear file input
        if (this.elements.fileUpload) {
            this.elements.fileUpload.value = '';
        }

        // Hide file info
        if (this.elements.fileInfo) {
            this.elements.fileInfo.classList.add('hidden');
        }

        // Reset state
        this.domains = [];
        this.validDomains = [];
        this.invalidDomains = [];
        this.fileContent = null;

        // Update UI
        this.updateUI();

        // Trigger callback
        if (this.onDomainsChange) {
            this.onDomainsChange({
                domains: [],
                validDomains: [],
                invalidDomains: []
            });
        }
    }

    /**
     * Handles errors
     * @param {Error} error - Error to handle
     */
    handleError(error) {
        console.error('DomainInput error:', error);
        
        if (this.onError) {
            this.onError(error);
        }

        // Show error message (could be enhanced with toast notification)
        alert(`Error: ${error.message}`);
    }

    /**
     * Gets current state
     * @returns {Object} Current state
     */
    getState() {
        return {
            domains: [...this.domains],
            validDomains: [...this.validDomains],
            invalidDomains: [...this.invalidDomains],
            hasFile: this.fileContent !== null,
            totalCount: this.domains.length,
            validCount: this.validDomains.length,
            invalidCount: this.invalidDomains.length,
            isOverLimit: this.validDomains.length > this.config.maxDomains
        };
    }

    /**
     * Sets domains programmatically
     * @param {Array<string>} domains - Domains to set
     */
    setDomains(domains) {
        if (!Array.isArray(domains)) {
            throw new Error('Domains must be an array');
        }

        // Update textarea
        if (this.elements.textarea) {
            this.elements.textarea.value = domains.join('\n');
        }

        // Process domains
        this.extractDomains(domains.join('\n'));
        this.validateDomains();
        this.updateUI();

        // Trigger callback
        if (this.onDomainsChange) {
            this.onDomainsChange({
                domains: this.domains,
                validDomains: this.validDomains,
                invalidDomains: this.invalidDomains
            });
        }
    }

    /**
     * Gets validation errors for invalid domains
     * @returns {Array<Object>} Validation errors
     */
    getValidationErrors() {
        return this.invalidDomains.map((domain, index) => ({
            index,
            domain,
            errors: this.getErrorsForDomain(domain)
        }));
    }

    /**
     * Gets errors for a specific domain
     * @param {string} domain - Domain to check
     * @returns {Array<string>} Error messages
     */
    getErrorsForDomain(domain) {
        const errors = [];
        const normalized = this.normalizeDomain(domain);

        if (!normalized) {
            errors.push('Empty domain');
            return errors;
        }

        if (normalized.length > 253) {
            errors.push('Domain too long (max 253 characters)');
        }

        if (normalized.includes('..')) {
            errors.push('Contains consecutive dots');
        }

        if (normalized.startsWith('-') || normalized.endsWith('-')) {
            errors.push('Cannot start or end with hyphen');
        }

        const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/;
        if (!domainRegex.test(normalized)) {
            errors.push('Invalid domain format');
        }

        const labels = normalized.split('.');
        for (let i = 0; i < labels.length; i++) {
            const label = labels[i];
            if (label.length === 0) {
                errors.push(`Empty label at position ${i + 1}`);
            } else if (label.length > 63) {
                errors.push(`Label '${label}' exceeds 63 characters`);
            }
        }

        return errors;
    }
}
