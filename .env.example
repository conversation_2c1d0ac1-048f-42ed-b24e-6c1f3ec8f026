# Domain Availability Checker Environment Variables

# App Settings
DEBUG=False
APP_NAME="Domain Availability Checker"

# Selenium Settings
CHROME_DRIVER_PATH=""
HEADLESS_MODE=True
SELENIUM_TIMEOUT=30
PAGE_LOAD_TIMEOUT=60

# Domain Checking Settings
MAX_DOMAINS_PER_BATCH=5000
MAX_CONCURRENT_BATCHES=1
RESULT_CHECK_INTERVAL=2
MAX_RESULT_WAIT_CYCLES=5
IDLE_WAIT_TIME=2000

# File Settings
UPLOAD_DIR="uploads"
DOWNLOAD_DIR="downloads"
TEMP_DIR="temp"
MAX_FILE_SIZE=10485760
