Collecting pydantic-core==2.14.1 (from pydantic==2.5.0->-r requirements-minimal.txt (line 10))
  Using cached pydantic_core-2.14.1.tar.gz (359 kB)
  Installing build dependencies ... done
  Getting requirements to build wheel ... done
  Installing backend dependencies ... done
  Preparing metadata (pyproject.toml) ... error
  error: subprocess-exited-with-error

  × Preparing metadata (pyproject.toml) did not run successfully.
  │ exit code: 1
  ╰─> [23 lines of output]
      Python reports SOABI: cp313-win_amd64
      Computed rustc target triple: x86_64-pc-windows-msvc
      Installation directory: C:\Users\<USER>\AppData\Local\puccinialin\puccinialin\Cache
      Rustup already downloaded
      Installing rust to C:\Users\<USER>\AppData\Local\puccinialin\puccinialin\Cache\rustup
      warn: It looks like you have an existing rustup settings file at:
      warn: C:\Users\<USER>\.rustup\settings.toml
      warn: Rustup will install the default toolchain as specified in the settings file,
      warn: instead of the one inferred from the default host triple.
      warn: installing msvc toolchain without its prerequisites
      info: profile set to 'minimal'
      info: default host triple is x86_64-pc-windows-msvc
      warn: Updating existing toolchain, profile choice will be ignored
      info: syncing channel updates for 'stable-x86_64-pc-windows-msvc'
      info: default toolchain set to 'stable-x86_64-pc-windows-msvc'
      Checking if cargo is installed

      Cargo, the Rust package manager, is not installed or is not on PATH.
      This package requires Rust and Cargo to compile extensions. Install it through
      the system's package manager or via https://rustup.rs/

      Checking for Rust toolchain....
      Rust not found, installing into a temporary directory
      [end of output]

  note: This error originates from a subprocess, and is likely not a problem with pip.
error: metadata-generation-failed

× Encountered error while generating package metadata.
╰─> See above for output.

note: This is an issue with the package mentioned above, not pip.
hint: See above for details.

[3/4] Creating directories...

[4/4] Creating environment file...
        1 file(s) copied.
Environment file created from template

========================================
Installation completed successfully!
========================================

To start the application:
  python run.py

Then open your browser and go to:
  http://localhost:8000

Press any key to continue . .