/**
 * Domain Checker Frontend JavaScript
 */

function domainChecker() {
    return {
        // Data properties
        domainList: '',
        jobName: '',
        currentJob: null,
        results: [],
        isProcessing: false,
        displayLimit: 50,
        websocket: null,
        notification: {
            show: false,
            message: '',
            type: 'success'
        },

        // Computed properties
        get domainCount() {
            if (!this.domainList.trim()) return 0;
            return this.domainList.trim().split('\n').filter(domain => domain.trim()).length;
        },

        // Initialize component
        init() {
            console.log('Domain Checker initialized');
            this.loadJobFromStorage();
        },

        // Start domain checking process
        async startDomainCheck() {
            if (!this.domainList.trim()) {
                this.showNotification('Vui lòng nhập danh sách domain', 'error');
                return;
            }

            const domains = this.domainList.trim().split('\n')
                .map(domain => domain.trim())
                .filter(domain => domain);

            if (domains.length === 0) {
                this.showNotification('Không có domain hợp lệ nào được tìm thấy', 'error');
                return;
            }

            try {
                this.isProcessing = true;
                this.results = [];

                const response = await fetch('/api/check-domains', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        domains: domains,
                        job_name: this.jobName || null
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const jobResponse = await response.json();
                this.currentJob = {
                    job_id: jobResponse.job_id,
                    job_name: this.jobName,
                    status: jobResponse.status,
                    total_domains: jobResponse.total_domains,
                    total_batches: jobResponse.total_batches,
                    processed_domains: 0,
                    completed_batches: 0,
                    progress_percentage: 0,
                    estimated_time_remaining: null,
                    current_batch: null,
                    results_file_url: null
                };

                this.saveJobToStorage();
                this.connectWebSocket(jobResponse.job_id);
                this.showNotification(`Công việc đã được tạo với ID: ${jobResponse.job_id}`, 'success');

            } catch (error) {
                console.error('Error starting domain check:', error);
                this.showNotification('Lỗi khi bắt đầu kiểm tra domain: ' + error.message, 'error');
                this.isProcessing = false;
            }
        },

        // Connect to WebSocket for real-time updates
        connectWebSocket(jobId) {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/progress/${jobId}`;

            this.websocket = new WebSocket(wsUrl);

            this.websocket.onopen = () => {
                console.log('WebSocket connected');
            };

            this.websocket.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    this.handleWebSocketMessage(message);
                } catch (error) {
                    console.error('Error parsing WebSocket message:', error);
                }
            };

            this.websocket.onclose = () => {
                console.log('WebSocket disconnected');
                // Try to reconnect after 5 seconds if job is still processing
                if (this.currentJob && 
                    (this.currentJob.status === 'processing' || this.currentJob.status === 'pending')) {
                    setTimeout(() => {
                        this.connectWebSocket(jobId);
                    }, 5000);
                }
            };

            this.websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
            };
        },

        // Handle WebSocket messages
        handleWebSocketMessage(message) {
            console.log('WebSocket message:', message);

            switch (message.type) {
                case 'progress':
                    this.updateJobProgress(message.data);
                    break;
                case 'completed':
                    this.handleJobCompletion(message.data);
                    break;
                case 'error':
                    this.handleJobError(message.data);
                    break;
                case 'connected':
                case 'heartbeat':
                case 'pong':
                    // Ignore these message types
                    break;
                default:
                    console.log('Unknown message type:', message.type);
            }
        },

        // Update job progress
        updateJobProgress(data) {
            if (this.currentJob && this.currentJob.job_id === data.job_id) {
                Object.assign(this.currentJob, data);
                this.saveJobToStorage();
            }
        },

        // Handle job completion
        async handleJobCompletion(data) {
            this.updateJobProgress(data);
            this.isProcessing = false;
            this.showNotification('Kiểm tra domain hoàn tất!', 'success');
            
            // Load results
            await this.loadJobResults();
        },

        // Handle job error
        handleJobError(data) {
            this.updateJobProgress(data);
            this.isProcessing = false;
            this.showNotification('Lỗi khi kiểm tra domain: ' + (data.error_message || 'Unknown error'), 'error');
        },

        // Load job results
        async loadJobResults() {
            if (!this.currentJob || !this.currentJob.job_id) return;

            try {
                const response = await fetch(`/api/job/${this.currentJob.job_id}/results`);
                if (response.ok) {
                    const resultsData = await response.json();
                    this.results = resultsData.results || [];
                    console.log('Loaded results:', this.results.length);
                }
            } catch (error) {
                console.error('Error loading results:', error);
            }
        },

        // Cancel current job
        async cancelJob() {
            if (!this.currentJob || !this.currentJob.job_id) return;

            try {
                const response = await fetch(`/api/job/${this.currentJob.job_id}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    this.currentJob.status = 'cancelled';
                    this.isProcessing = false;
                    this.showNotification('Công việc đã được hủy', 'success');
                    this.saveJobToStorage();
                } else {
                    throw new Error('Failed to cancel job');
                }
            } catch (error) {
                console.error('Error cancelling job:', error);
                this.showNotification('Lỗi khi hủy công việc: ' + error.message, 'error');
            }
        },

        // Download results
        downloadResults() {
            if (!this.currentJob || !this.currentJob.results_file_url) return;

            const link = document.createElement('a');
            link.href = this.currentJob.results_file_url;
            link.download = `domain_results_${this.currentJob.job_id}.csv`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        },

        // Utility functions
        getStatusClass(status) {
            const classes = {
                'pending': 'bg-yellow-100 text-yellow-800',
                'processing': 'bg-blue-100 text-blue-800',
                'completed': 'bg-green-100 text-green-800',
                'failed': 'bg-red-100 text-red-800',
                'cancelled': 'bg-gray-100 text-gray-800'
            };
            return classes[status] || 'bg-gray-100 text-gray-800';
        },

        getStatusText(status) {
            const texts = {
                'pending': 'Đang chờ',
                'processing': 'Đang xử lý',
                'completed': 'Hoàn thành',
                'failed': 'Thất bại',
                'cancelled': 'Đã hủy'
            };
            return texts[status] || 'Không xác định';
        },

        formatTime(seconds) {
            if (!seconds || seconds <= 0) return 'N/A';
            
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;

            if (hours > 0) {
                return `${hours}h ${minutes}m`;
            } else if (minutes > 0) {
                return `${minutes}m ${secs}s`;
            } else {
                return `${secs}s`;
            }
        },

        showNotification(message, type = 'success') {
            this.notification = {
                show: true,
                message: message,
                type: type
            };

            // Auto hide after 5 seconds
            setTimeout(() => {
                this.notification.show = false;
            }, 5000);
        },

        // Local storage functions
        saveJobToStorage() {
            if (this.currentJob) {
                localStorage.setItem('currentJob', JSON.stringify(this.currentJob));
            }
        },

        loadJobFromStorage() {
            const savedJob = localStorage.getItem('currentJob');
            if (savedJob) {
                try {
                    this.currentJob = JSON.parse(savedJob);
                    
                    // If job is still processing, reconnect WebSocket
                    if (this.currentJob.status === 'processing' || this.currentJob.status === 'pending') {
                        this.isProcessing = true;
                        this.connectWebSocket(this.currentJob.job_id);
                    } else if (this.currentJob.status === 'completed') {
                        this.loadJobResults();
                    }
                } catch (error) {
                    console.error('Error loading job from storage:', error);
                    localStorage.removeItem('currentJob');
                }
            }
        },

        clearJobFromStorage() {
            localStorage.removeItem('currentJob');
            this.currentJob = null;
            this.results = [];
            this.isProcessing = false;
        }
    };
}
