/**
 * Results Table Component
 * Handles display of wayback machine results in a table format
 */
export class ResultsTable {
    constructor(options = {}) {
        this.config = {
            pageSize: 50,
            enablePagination: false,
            enableSorting: true,
            enableFiltering: false,
            showRowNumbers: true,
            animateRows: true,
            ...options
        };

        // State
        this.results = [];
        this.filteredResults = [];
        this.currentPage = 1;
        this.sortColumn = null;
        this.sortDirection = 'asc';
        this.filter = '';

        // DOM elements
        this.elements = {};
        
        // Event handlers
        this.onRowClick = null;
        this.onSort = null;
        this.onFilter = null;
        
        this.init();
    }

    /**
     * Initializes the component
     */
    init() {
        this.bindElements();
        this.setupEventListeners();
        this.showEmptyState();
    }

    /**
     * Binds DOM elements
     */
    bindElements() {
        this.elements = {
            tbody: document.getElementById('results-table-body'),
            table: document.querySelector('#results-table-body').closest('table'),
            container: document.querySelector('#results-table-body').closest('.overflow-x-auto')
        };
    }

    /**
     * Sets up event listeners
     */
    setupEventListeners() {
        // Table header clicks for sorting
        if (this.config.enableSorting && this.elements.table) {
            const headers = this.elements.table.querySelectorAll('thead th');
            headers.forEach((header, index) => {
                if (index > 0) { // Skip STT column
                    header.style.cursor = 'pointer';
                    header.addEventListener('click', () => {
                        this.handleSort(index);
                    });
                }
            });
        }
    }

    /**
     * Shows empty state
     */
    showEmptyState() {
        if (!this.elements.tbody) return;

        this.elements.tbody.innerHTML = `
            <tr>
                <td colspan="5" class="px-6 py-12 text-center text-gray-500">
                    <div class="flex flex-col items-center">
                        <svg class="h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <p class="text-lg font-medium">Chưa có dữ liệu</p>
                        <p class="text-sm">Nhập domain và bắt đầu kiểm tra để xem kết quả</p>
                    </div>
                </td>
            </tr>
        `;
    }

    /**
     * Shows loading state
     * @param {number} count - Number of skeleton rows to show
     */
    showLoadingState(count = 5) {
        if (!this.elements.tbody) return;

        const skeletonRows = Array.from({ length: count }, (_, index) => `
            <tr class="animate-pulse">
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="h-4 bg-gray-200 rounded w-8"></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="h-4 bg-gray-200 rounded w-32"></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="h-4 bg-gray-200 rounded w-16"></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="h-4 bg-gray-200 rounded w-24"></div>
                </td>
                <td class="px-6 py-4">
                    <div class="h-4 bg-gray-200 rounded w-40"></div>
                </td>
            </tr>
        `).join('');

        this.elements.tbody.innerHTML = skeletonRows;
    }

    /**
     * Adds a single result row
     * @param {Object} result - Result data
     * @param {number} index - Row index
     */
    addRow(result, index) {
        if (!this.elements.tbody) return;

        // Remove empty state if it exists
        if (this.results.length === 0) {
            this.elements.tbody.innerHTML = '';
        }

        this.results.push(result);
        this.filteredResults = [...this.results];

        const row = this.createResultRow(result, index);
        
        if (this.config.animateRows) {
            row.style.opacity = '0';
            row.style.transform = 'translateY(10px)';
        }

        this.elements.tbody.appendChild(row);

        // Animate row in
        if (this.config.animateRows) {
            requestAnimationFrame(() => {
                row.style.transition = 'all 0.3s ease';
                row.style.opacity = '1';
                row.style.transform = 'translateY(0)';
            });
        }

        this.updateTableState();
    }

    /**
     * Creates a result row element
     * @param {Object} result - Result data
     * @param {number} index - Row index
     * @returns {HTMLElement} Row element
     */
    createResultRow(result, index) {
        const row = document.createElement('tr');
        row.className = 'hover:bg-gray-50 transition-colors';
        row.dataset.domain = result.domain;

        // Format data
        const statusCodesText = this.formatStatusCodes(result.statusCodes, result.error);
        const timeRange = this.formatTimeRange(result);
        const snapshotDisplay = this.formatSnapshotCount(result);

        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                ${index + 1}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                <div class="flex items-center">
                    <span class="truncate max-w-xs" title="${result.domain}">${result.domain}</span>
                    ${this.getDomainStatusIcon(result)}
                </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                ${snapshotDisplay}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                ${timeRange}
            </td>
            <td class="px-6 py-4 text-sm text-gray-900">
                <div class="max-w-xs truncate" title="${statusCodesText}">
                    ${statusCodesText}
                </div>
            </td>
        `;

        // Add click handler
        row.addEventListener('click', () => {
            this.handleRowClick(result, index);
        });

        return row;
    }

    /**
     * Gets domain status icon
     * @param {Object} result - Result data
     * @returns {string} Icon HTML
     */
    getDomainStatusIcon(result) {
        if (result.error) {
            return '<span class="ml-2 text-red-500" title="Error">❌</span>';
        } else if (result.snapshotCount === 0) {
            return '<span class="ml-2 text-yellow-500" title="No snapshots">⚠️</span>';
        } else {
            return '<span class="ml-2 text-green-500" title="Success">✅</span>';
        }
    }

    /**
     * Formats snapshot count for display
     * @param {Object} result - Result data
     * @returns {string} Formatted snapshot count
     */
    formatSnapshotCount(result) {
        if (result.error) {
            return '<span class="text-red-600 font-medium">Error</span>';
        } else if (result.snapshotCount === 0) {
            return '<span class="text-yellow-600">0</span>';
        } else {
            return `<span class="text-green-600 font-medium">${result.snapshotCount.toLocaleString()}</span>`;
        }
    }

    /**
     * Formats time range for display
     * @param {Object} result - Result data
     * @returns {string} Formatted time range
     */
    formatTimeRange(result) {
        if (result.error || !result.firstSnapshot || !result.lastSnapshot) {
            return '<span class="text-gray-400">N/A</span>';
        }

        if (result.firstSnapshot === result.lastSnapshot) {
            return result.firstSnapshot;
        }

        return `${result.firstSnapshot} - ${result.lastSnapshot}`;
    }

    /**
     * Formats status codes for display
     * @param {Object} statusCodes - Status code counts
     * @param {string} error - Error message
     * @returns {string} Formatted status codes
     */
    formatStatusCodes(statusCodes, error) {
        if (error) {
            return `<span class="text-red-600">${error}</span>`;
        }

        if (!statusCodes || Object.keys(statusCodes).length === 0) {
            return '<span class="text-gray-400">N/A</span>';
        }

        const entries = Object.entries(statusCodes)
            .sort(([a], [b]) => a.localeCompare(b))
            .map(([code, count]) => {
                const color = this.getStatusCodeColor(code);
                return `<span class="${color}">${code}:${count}</span>`;
            });

        return entries.join(' - ');
    }

    /**
     * Gets color class for status code
     * @param {string} code - Status code
     * @returns {string} Color class
     */
    getStatusCodeColor(code) {
        const codeNum = parseInt(code);
        
        if (codeNum >= 200 && codeNum < 300) {
            return 'text-green-600'; // Success
        } else if (codeNum >= 300 && codeNum < 400) {
            return 'text-blue-600'; // Redirect
        } else if (codeNum >= 400 && codeNum < 500) {
            return 'text-yellow-600'; // Client error
        } else if (codeNum >= 500) {
            return 'text-red-600'; // Server error
        } else {
            return 'text-gray-600'; // Unknown
        }
    }

    /**
     * Handles row click
     * @param {Object} result - Result data
     * @param {number} index - Row index
     */
    handleRowClick(result, index) {
        // Highlight clicked row
        const rows = this.elements.tbody.querySelectorAll('tr');
        rows.forEach(row => row.classList.remove('bg-blue-50'));
        
        if (rows[index]) {
            rows[index].classList.add('bg-blue-50');
        }

        // Trigger callback
        if (this.onRowClick) {
            this.onRowClick(result, index);
        }
    }

    /**
     * Handles column sorting
     * @param {number} columnIndex - Column index to sort by
     */
    handleSort(columnIndex) {
        const columns = ['', 'domain', 'snapshotCount', 'timeRange', 'statusCodes'];
        const column = columns[columnIndex];
        
        if (!column) return;

        // Toggle sort direction
        if (this.sortColumn === column) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = column;
            this.sortDirection = 'asc';
        }

        this.sortResults();
        this.renderResults();
        this.updateSortHeaders();

        // Trigger callback
        if (this.onSort) {
            this.onSort(this.sortColumn, this.sortDirection);
        }
    }

    /**
     * Sorts results based on current sort settings
     */
    sortResults() {
        this.filteredResults.sort((a, b) => {
            let aValue, bValue;

            switch (this.sortColumn) {
                case 'domain':
                    aValue = a.domain;
                    bValue = b.domain;
                    break;
                case 'snapshotCount':
                    aValue = a.error ? -1 : a.snapshotCount;
                    bValue = b.error ? -1 : b.snapshotCount;
                    break;
                case 'timeRange':
                    aValue = a.firstSnapshot || '';
                    bValue = b.firstSnapshot || '';
                    break;
                case 'statusCodes':
                    aValue = a.error || Object.keys(a.statusCodes || {}).join('');
                    bValue = b.error || Object.keys(b.statusCodes || {}).join('');
                    break;
                default:
                    return 0;
            }

            // Handle string vs number comparison
            if (typeof aValue === 'number' && typeof bValue === 'number') {
                return this.sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
            } else {
                const comparison = String(aValue).localeCompare(String(bValue));
                return this.sortDirection === 'asc' ? comparison : -comparison;
            }
        });
    }

    /**
     * Updates sort header indicators
     */
    updateSortHeaders() {
        if (!this.elements.table) return;

        const headers = this.elements.table.querySelectorAll('thead th');
        const columns = ['', 'domain', 'snapshotCount', 'timeRange', 'statusCodes'];

        headers.forEach((header, index) => {
            const column = columns[index];
            const sortIndicator = header.querySelector('.sort-indicator');
            
            // Remove existing indicator
            if (sortIndicator) {
                sortIndicator.remove();
            }

            // Add new indicator if this is the sort column
            if (column && column === this.sortColumn) {
                const indicator = document.createElement('span');
                indicator.className = 'sort-indicator ml-2';
                indicator.innerHTML = this.sortDirection === 'asc' ? '▲' : '▼';
                header.appendChild(indicator);
            }
        });
    }

    /**
     * Renders all results
     */
    renderResults() {
        if (!this.elements.tbody) return;

        this.elements.tbody.innerHTML = '';

        if (this.filteredResults.length === 0) {
            this.showEmptyState();
            return;
        }

        this.filteredResults.forEach((result, index) => {
            const row = this.createResultRow(result, index);
            this.elements.tbody.appendChild(row);
        });

        this.updateTableState();
    }

    /**
     * Updates table state and statistics
     */
    updateTableState() {
        // Update table container classes for scrolling
        if (this.elements.container && this.results.length > 0) {
            this.elements.container.classList.add('border');
        }
    }

    /**
     * Sets all results at once
     * @param {Array} results - Array of result objects
     */
    setResults(results) {
        this.results = [...results];
        this.filteredResults = [...results];
        this.renderResults();
    }

    /**
     * Clears all results
     */
    clearResults() {
        this.results = [];
        this.filteredResults = [];
        this.currentPage = 1;
        this.sortColumn = null;
        this.sortDirection = 'asc';
        this.showEmptyState();
    }

    /**
     * Filters results based on search term
     * @param {string} searchTerm - Search term
     */
    filterResults(searchTerm = '') {
        this.filter = searchTerm.toLowerCase();

        if (!this.filter) {
            this.filteredResults = [...this.results];
        } else {
            this.filteredResults = this.results.filter(result => {
                return result.domain.toLowerCase().includes(this.filter) ||
                       (result.error && result.error.toLowerCase().includes(this.filter));
            });
        }

        this.renderResults();

        // Trigger callback
        if (this.onFilter) {
            this.onFilter(this.filter, this.filteredResults.length);
        }
    }

    /**
     * Gets table statistics
     * @returns {Object} Table statistics
     */
    getStats() {
        const total = this.results.length;
        const success = this.results.filter(r => !r.error).length;
        const errors = this.results.filter(r => r.error).length;
        const totalSnapshots = this.results.reduce((sum, r) => sum + (r.snapshotCount || 0), 0);

        return {
            total,
            success,
            errors,
            successRate: total > 0 ? Math.round((success / total) * 100) : 0,
            totalSnapshots,
            averageSnapshots: success > 0 ? Math.round(totalSnapshots / success) : 0,
            filtered: this.filteredResults.length,
            isFiltered: this.filter.length > 0
        };
    }

    /**
     * Exports visible results
     * @param {string} format - Export format
     * @returns {string} Exported data
     */
    exportVisibleResults(format = 'csv') {
        const headers = ['STT', 'Domain', 'Snapshot', 'Thời gian', 'StatusCode'];
        const data = this.filteredResults.map((result, index) => [
            index + 1,
            result.domain,
            result.error ? 'Error' : result.snapshotCount,
            this.formatTimeRange(result).replace(/<[^>]*>/g, ''), // Strip HTML
            this.formatStatusCodes(result.statusCodes, result.error).replace(/<[^>]*>/g, '') // Strip HTML
        ]);

        switch (format.toLowerCase()) {
            case 'csv':
                return this.exportToCSV([headers, ...data]);
            case 'tsv':
                return this.exportToTSV([headers, ...data]);
            case 'json':
                return JSON.stringify(this.filteredResults, null, 2);
            default:
                throw new Error(`Unsupported format: ${format}`);
        }
    }

    /**
     * Exports data to CSV format
     * @param {Array} data - Data to export
     * @returns {string} CSV content
     */
    exportToCSV(data) {
        return data.map(row => 
            row.map(cell => `"${String(cell).replace(/"/g, '""')}"`)
               .join(',')
        ).join('\n');
    }

    /**
     * Exports data to TSV format
     * @param {Array} data - Data to export
     * @returns {string} TSV content
     */
    exportToTSV(data) {
        return data.map(row => 
            row.map(cell => String(cell)).join('\t')
        ).join('\n');
    }

    /**
     * Highlights a specific domain
     * @param {string} domain - Domain to highlight
     */
    highlightDomain(domain) {
        const rows = this.elements.tbody.querySelectorAll('tr');
        
        rows.forEach(row => {
            row.classList.remove('bg-yellow-100', 'border-yellow-300');
            
            if (row.dataset.domain === domain) {
                row.classList.add('bg-yellow-100', 'border-yellow-300');
                row.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        });
    }

    /**
     * Updates a specific result
     * @param {string} domain - Domain to update
     * @param {Object} newResult - New result data
     */
    updateResult(domain, newResult) {
        const index = this.results.findIndex(r => r.domain === domain);
        
        if (index !== -1) {
            this.results[index] = { ...this.results[index], ...newResult };
            this.filteredResults = [...this.results];
            this.renderResults();
        }
    }

    /**
     * Removes a specific result
     * @param {string} domain - Domain to remove
     */
    removeResult(domain) {
        this.results = this.results.filter(r => r.domain !== domain);
        this.filteredResults = this.filteredResults.filter(r => r.domain !== domain);
        this.renderResults();
    }
}
