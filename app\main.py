"""
Domain Availability Checker - Main FastAPI Application
"""
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
import os

# Import routers
from app.routers import domain_checker, websocket_handler

# Create FastAPI app
app = FastAPI(
    title="Domain Availability Checker",
    description="Automated domain availability checking using Namecheap bulk search",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# Setup templates
templates = Jinja2Templates(directory="app/templates")

# Include routers
app.include_router(domain_checker.router, prefix="/api", tags=["domain-checker"])
app.include_router(websocket_handler.router, prefix="/ws", tags=["websocket"])

@app.get("/")
async def root(request: Request):
    """Serve the main page"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "message": "Domain Checker API is running"}

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
