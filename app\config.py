"""
Configuration settings for Domain Availability Checker
"""
import os
from pathlib import Path
try:
    from pydantic_settings import BaseSettings
except ImportError:
    # Fallback for older pydantic versions
    from pydantic import BaseSettings

class Settings(BaseSettings):
    """Application settings"""
    
    # App settings
    app_name: str = "Domain Availability Checker"
    debug: bool = False
    
    # Selenium settings
    chrome_driver_path: str = ""
    headless_mode: bool = True
    selenium_timeout: int = 30
    page_load_timeout: int = 60
    
    # Domain checking settings
    max_domains_per_batch: int = 5000
    max_concurrent_batches: int = 1
    result_check_interval: int = 2
    max_result_wait_cycles: int = 5
    idle_wait_time: int = 2000
    
    # File settings
    upload_dir: str = "uploads"
    download_dir: str = "downloads"
    temp_dir: str = "temp"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    
    # Namecheap URLs and selectors
    namecheap_bulk_search_url: str = "https://www.namecheap.com/domains/bulk-domain-search/"
    
    # XPath selectors for Namecheap
    popup_selectors: list = [
        "//[@id='react-nc-search']/div/section/div[2]/div/button",
        "//[@id='react-nc-search']/div/section/div[2]/div/div/button[1]"
    ]
    domain_input_selector: str = "//*[@id='beast-keywords-input']"
    uncheck_option_selector: str = "//[@id='beast-options']/label[1]/input"
    check_option_selector: str = "//[@id='beast-options']/label[2]/input"
    search_button_selector: str = "//*[@id='react-nc-search']/div/section/div/form/button"
    export_button_selector: str = "//*[@id='search-results']/header/div[2]/div/button[2]"
    load_more_button_selector: str = "//button[contains(text(), 'Load More')]"
    
    class Config:
        env_file = ".env"
        case_sensitive = False

# Create settings instance
settings = Settings()

# Ensure directories exist
for directory in [settings.upload_dir, settings.download_dir, settings.temp_dir]:
    Path(directory).mkdir(parents=True, exist_ok=True)
