// Import các modules
import { DomainValidator } from './utils/DomainValidator.js';
import { DateFormatter } from './utils/DateFormatter.js';
import { ExportHelper } from './utils/ExportHelper.js';
import { WaybackAPI } from './services/WaybackAPI.js';
import { RateLimiter } from './services/RateLimiter.js';
import { DataProcessor } from './services/DataProcessor.js';

const HTML_TEMPLATE = `
<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wayback Machine Snapshot Checker</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .loading-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        .toast {
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }
        .toast.show {
            transform: translateX(0);
        }
        
        .progress-bar {
            transition: width 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Toast Container -->
    <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>

    <div class="container mx-auto px-4 py-8 max-w-7xl">
        <!-- Header -->
        <header class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-800 mb-4">
                🔍 Wayback Machine Snapshot Checker
            </h1>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                Kiểm tra snapshot của domain từ Wayback Machine API, hỗ trợ xử lý hàng loạt tối đa 5000 domain
            </p>
        </header>

        <!-- Input Section -->
        <section class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">📝 Nhập Domain</h2>
            
            <div class="grid md:grid-cols-2 gap-6">
                <!-- Textarea Input -->
                <div>
                    <label for="domain-textarea" class="block text-sm font-medium text-gray-700 mb-2">
                        Nhập domain (mỗi domain một dòng)
                    </label>
                    <textarea
                        id="domain-textarea"
                        placeholder="example1.com&#10;example2.com&#10;example3.com"
                        class="w-full h-40 p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
                        maxlength="100000"
                    ></textarea>
                    <div class="mt-2 flex justify-between text-sm text-gray-500">
                        <span id="domain-count">0 domain</span>
                        <span id="char-count">0/100000 ký tự</span>
                    </div>
                </div>

                <!-- File Upload -->
                <div>
                    <label for="file-upload" class="block text-sm font-medium text-gray-700 mb-2">
                        Hoặc tải file (.txt, .csv)
                    </label>
                    <div class="border-2 border-dashed border-gray-300 rounded-md p-6 text-center hover:border-primary-500 transition-colors">
                        <input
                            type="file"
                            id="file-upload"
                            accept=".txt,.csv"
                            class="hidden"
                        />
                        <label for="file-upload" class="cursor-pointer">
                            <svg class="mx-auto h-12 w-12 text-gray-400 mb-2" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                            <span class="text-primary-600 font-medium">Click để chọn file</span>
                            <p class="text-gray-500 text-sm mt-1">Hoặc kéo thả file vào đây</p>
                        </label>
                    </div>
                    <div id="file-info" class="mt-2 text-sm text-gray-600 hidden"></div>
                </div>
            </div>

            <!-- Validation Status -->
            <div id="validation-status" class="mt-4 p-3 rounded-md hidden">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <span id="valid-count" class="text-green-600 font-medium">✓ 0 domain hợp lệ</span>
                        <span id="invalid-count" class="text-red-600 font-medium">✗ 0 domain không hợp lệ</span>
                    </div>
                    <button id="clear-btn" class="text-gray-500 hover:text-gray-700 text-sm">
                        🗑️ Xóa tất cả
                    </button>
                </div>
            </div>
        </section>

        <!-- Control Panel -->
        <section class="bg-white rounded-lg shadow-md p-6 mb-8">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <div class="flex space-x-3">
                    <button id="start-btn" class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-md font-medium transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed">
                        ▶️ Bắt đầu kiểm tra
                    </button>
                    <button id="pause-btn" class="bg-yellow-600 hover:bg-yellow-700 text-white px-6 py-2 rounded-md font-medium transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed hidden">
                        ⏸️ Tạm dừng
                    </button>
                    <button id="resume-btn" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md font-medium transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed hidden">
                        ▶️ Tiếp tục
                    </button>
                    <button id="stop-btn" class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-md font-medium transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed hidden">
                        ⏹️ Dừng
                    </button>
                </div>
                
                <div class="text-sm text-gray-600" id="status-text">
                    Sẵn sàng kiểm tra
                </div>
            </div>

            <!-- Progress Bar -->
            <div id="progress-section" class="mt-4 hidden">
                <div class="flex justify-between text-sm text-gray-600 mb-2">
                    <span id="progress-text">Đang xử lý...</span>
                    <span id="progress-percent">0%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div id="progress-bar" class="progress-bar bg-primary-600 h-2 rounded-full" style="width: 0%"></div>
                </div>
                <div class="flex justify-between text-xs text-gray-500 mt-1">
                    <span id="progress-details">0/0 domain</span>
                    <span id="eta">Ước tính: --</span>
                </div>
            </div>
        </section>

        <!-- Results Section -->
        <section class="bg-white rounded-lg shadow-md mb-8">
            <div class="p-6 border-b border-gray-200">
                <div class="flex flex-wrap items-center justify-between gap-4">
                    <h2 class="text-2xl font-semibold text-gray-800">📊 Kết quả kiểm tra</h2>
                    
                    <div class="flex space-x-3" id="export-buttons">
                        <button id="copy-btn" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed">
                            📋 Copy
                        </button>
                        <button id="export-csv-btn" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed">
                            📄 Export CSV
                        </button>
                        <button id="export-excel-btn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed">
                            📊 Export Excel
                        </button>
                    </div>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">STT</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Domain</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Snapshot</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thời gian</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status Code</th>
                        </tr>
                    </thead>
                    <tbody id="results-table-body" class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td colspan="5" class="px-6 py-12 text-center text-gray-500">
                                Chưa có dữ liệu. Nhập domain và bắt đầu kiểm tra.
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <!-- Footer -->
        <footer class="bg-white rounded-lg shadow-md p-6">
            <div class="grid md:grid-cols-3 gap-6 text-sm text-gray-600">
                <div>
                    <h3 class="font-semibold text-gray-800 mb-2">⚡ Rate Limiting</h3>
                    <p>Search API: 0.8 calls/second</p>
                    <p>Tuân thủ nghiêm ngặt Rate Limit của Wayback Machine</p>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-800 mb-2">🔗 API Endpoint</h3>
                    <p>web.archive.org/cdx/search/cdx</p>
                    <p>JSON output format</p>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-800 mb-2">📈 Performance</h3>
                    <p id="total-processed">Đã xử lý: 0 domain</p>
                    <p id="success-rate">Tỷ lệ thành công: 0%</p>
                </div>
            </div>
        </footer>
    </div>

    <script type="module">
        // Import các components và services (sẽ được inject)
        ${await getClientSideCode()}
    </script>
</body>
</html>
`;

// Function để lấy client-side code
async function getClientSideCode() {
    return `
        // Configuration
        const CONFIG = {
            RATE_LIMITS: {
                search_calls_per_second: 0.8,
                memento_calls_per_second: 8,
                timemap_calls_per_second: 1.33
            },
            MAX_DOMAINS: 5000,
            BATCH_SIZE: 10,
            RETRY_DELAYS: [2000, 4000, 8000],
            API_ENDPOINT: 'http://web.archive.org/cdx/search/cdx'
        };

        // Global state
        let appState = {
            domains: [],
            validDomains: [],
            invalidDomains: [],
            results: [],
            processing: false,
            paused: false,
            currentIndex: 0,
            queue: [],
            startTime: null
        };

        // DOM elements
        const elements = {
            domainTextarea: document.getElementById('domain-textarea'),
            fileUpload: document.getElementById('file-upload'),
            domainCount: document.getElementById('domain-count'),
            charCount: document.getElementById('char-count'),
            validationStatus: document.getElementById('validation-status'),
            validCount: document.getElementById('valid-count'),
            invalidCount: document.getElementById('invalid-count'),
            clearBtn: document.getElementById('clear-btn'),
            startBtn: document.getElementById('start-btn'),
            pauseBtn: document.getElementById('pause-btn'),
            resumeBtn: document.getElementById('resume-btn'),
            stopBtn: document.getElementById('stop-btn'),
            statusText: document.getElementById('status-text'),
            progressSection: document.getElementById('progress-section'),
            progressText: document.getElementById('progress-text'),
            progressPercent: document.getElementById('progress-percent'),
            progressBar: document.getElementById('progress-bar'),
            progressDetails: document.getElementById('progress-details'),
            eta: document.getElementById('eta'),
            resultsTableBody: document.getElementById('results-table-body'),
            copyBtn: document.getElementById('copy-btn'),
            exportCsvBtn: document.getElementById('export-csv-btn'),
            exportExcelBtn: document.getElementById('export-excel-btn'),
            totalProcessed: document.getElementById('total-processed'),
            successRate: document.getElementById('success-rate')
        };

        // Utility functions
        function validateDomain(domain) {
            const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\\.[a-zA-Z]{2,}$/;
            return domainRegex.test(domain.trim());
        }

        function formatDate(timestamp) {
            if (!timestamp || timestamp.length < 8) return '';
            const year = timestamp.substring(0, 4);
            const month = timestamp.substring(4, 6);
            const day = timestamp.substring(6, 8);
            return \`\${day}/\${month}/\${year}\`;
        }

        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = \`toast p-4 rounded-md shadow-lg text-white \${
                type === 'error' ? 'bg-red-600' : 
                type === 'success' ? 'bg-green-600' : 
                type === 'warning' ? 'bg-yellow-600' : 'bg-blue-600'
            }\`;
            toast.textContent = message;
            
            document.getElementById('toast-container').appendChild(toast);
            
            setTimeout(() => toast.classList.add('show'), 100);
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }

        function updateDomainCount() {
            const text = elements.domainTextarea.value;
            const domains = text.split('\\n').filter(line => line.trim());
            const validDomains = domains.filter(validateDomain);
            const invalidDomains = domains.filter(domain => domain.trim() && !validateDomain(domain));
            
            appState.domains = domains;
            appState.validDomains = validDomains;
            appState.invalidDomains = invalidDomains;
            
            elements.domainCount.textContent = \`\${domains.length} domain\`;
            elements.charCount.textContent = \`\${text.length}/100000 ký tự\`;
            
            if (domains.length > 0) {
                elements.validationStatus.classList.remove('hidden');
                elements.validCount.textContent = \`✓ \${validDomains.length} domain hợp lệ\`;
                elements.invalidCount.textContent = \`✗ \${invalidDomains.length} domain không hợp lệ\`;
                
                if (validDomains.length > CONFIG.MAX_DOMAINS) {
                    elements.validationStatus.className = 'mt-4 p-3 rounded-md bg-red-50 border border-red-200';
                    showToast(\`Chỉ được phép tối đa \${CONFIG.MAX_DOMAINS} domain\`, 'error');
                } else if (invalidDomains.length > 0) {
                    elements.validationStatus.className = 'mt-4 p-3 rounded-md bg-yellow-50 border border-yellow-200';
                } else {
                    elements.validationStatus.className = 'mt-4 p-3 rounded-md bg-green-50 border border-green-200';
                }
            } else {
                elements.validationStatus.classList.add('hidden');
            }
            
            updateUI();
        }

        function updateUI() {
            const hasValidDomains = appState.validDomains.length > 0 && appState.validDomains.length <= CONFIG.MAX_DOMAINS;
            const isProcessing = appState.processing;
            const isPaused = appState.paused;
            
            elements.startBtn.disabled = !hasValidDomains || isProcessing;
            elements.pauseBtn.classList.toggle('hidden', !isProcessing || isPaused);
            elements.resumeBtn.classList.toggle('hidden', !isPaused);
            elements.stopBtn.classList.toggle('hidden', !isProcessing);
            
            elements.progressSection.classList.toggle('hidden', !isProcessing && !isPaused && appState.results.length === 0);
            
            const exportButtons = [elements.copyBtn, elements.exportCsvBtn, elements.exportExcelBtn];
            exportButtons.forEach(btn => {
                btn.disabled = appState.results.length === 0;
            });
        }

        async function fetchWaybackData(domain) {
            const url = \`\${CONFIG.API_ENDPOINT}?url=\${domain}&output=json\`;
            
            for (let attempt = 0; attempt < CONFIG.RETRY_DELAYS.length; attempt++) {
                try {
                    const response = await fetch(\`/api/wayback?domain=\${encodeURIComponent(domain)}\`);
                    
                    if (response.status === 429) {
                        const delay = CONFIG.RETRY_DELAYS[attempt];
                        await new Promise(resolve => setTimeout(resolve, delay));
                        continue;
                    }
                    
                    if (!response.ok) {
                        throw new Error(\`HTTP \${response.status}\`);
                    }
                    
                    const data = await response.json();
                    return data;
                } catch (error) {
                    console.error(\`Attempt \${attempt + 1} failed for \${domain}:\`, error);
                    if (attempt === CONFIG.RETRY_DELAYS.length - 1) {
                        throw error;
                    }
                    await new Promise(resolve => setTimeout(resolve, CONFIG.RETRY_DELAYS[attempt]));
                }
            }
        }

        function processWaybackData(domain, data) {
            if (!data || data.length <= 1) {
                return {
                    domain,
                    snapshotCount: 0,
                    firstSnapshot: '',
                    lastSnapshot: '',
                    statusCodes: {},
                    error: 'Không có dữ liệu'
                };
            }
            
            // Skip header row
            const snapshots = data.slice(1);
            const statusCodes = {};
            const timestamps = [];
            
            snapshots.forEach(row => {
                if (row.length >= 2) {
                    const timestamp = row[1];
                    const statusCode = row[4] || 'unknown';
                    
                    timestamps.push(timestamp);
                    statusCodes[statusCode] = (statusCodes[statusCode] || 0) + 1;
                }
            });
            
            timestamps.sort();
            
            return {
                domain,
                snapshotCount: snapshots.length,
                firstSnapshot: timestamps.length > 0 ? formatDate(timestamps[0]) : '',
                lastSnapshot: timestamps.length > 0 ? formatDate(timestamps[timestamps.length - 1]) : '',
                statusCodes
            };
        }

        function addResultRow(result, index) {
            const tbody = elements.resultsTableBody;
            
            // Remove empty state if exists
            if (tbody.children.length === 1 && tbody.children[0].children.length === 1) {
                tbody.innerHTML = '';
            }
            
            const row = document.createElement('tr');
            row.className = 'hover:bg-gray-50';
            
            const statusCodesText = Object.entries(result.statusCodes)
                .map(([code, count]) => \`\${code}:\${count}\`)
                .join(' - ') || 'N/A';
            
            const timeRange = result.firstSnapshot && result.lastSnapshot ? 
                \`\${result.firstSnapshot} - \${result.lastSnapshot}\` : 'N/A';
            
            row.innerHTML = \`
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">\${index + 1}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">\${result.domain}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    \${result.error ? \`<span class="text-red-600">Error</span>\` : result.snapshotCount}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">\${timeRange}</td>
                <td class="px-6 py-4 text-sm text-gray-900">\${result.error || statusCodesText}</td>
            \`;
            
            tbody.appendChild(row);
        }

        function updateProgress() {
            const total = appState.validDomains.length;
            const processed = appState.currentIndex;
            const percentage = total > 0 ? Math.round((processed / total) * 100) : 0;
            
            elements.progressPercent.textContent = \`\${percentage}%\`;
            elements.progressBar.style.width = \`\${percentage}%\`;
            elements.progressDetails.textContent = \`\${processed}/\${total} domain\`;
            
            // Calculate ETA
            if (appState.startTime && processed > 0) {
                const elapsed = Date.now() - appState.startTime;
                const avgTimePerDomain = elapsed / processed;
                const remaining = total - processed;
                const eta = remaining * avgTimePerDomain;
                
                const hours = Math.floor(eta / (1000 * 60 * 60));
                const minutes = Math.floor((eta % (1000 * 60 * 60)) / (1000 * 60));
                
                elements.eta.textContent = \`Ước tính: \${hours > 0 ? hours + 'h ' : ''}\${minutes}m\`;
            }
            
            // Update footer stats
            const successCount = appState.results.filter(r => !r.error).length;
            const successRate = processed > 0 ? Math.round((successCount / processed) * 100) : 0;
            
            elements.totalProcessed.textContent = \`Đã xử lý: \${processed} domain\`;
            elements.successRate.textContent = \`Tỷ lệ thành công: \${successRate}%\`;
        }

        async function processNextDomain() {
            if (appState.paused || appState.currentIndex >= appState.validDomains.length) {
                return false;
            }
            
            const domain = appState.validDomains[appState.currentIndex];
            elements.progressText.textContent = \`Đang xử lý: \${domain}\`;
            
            try {
                const data = await fetchWaybackData(domain);
                const result = processWaybackData(domain, data);
                
                appState.results.push(result);
                addResultRow(result, appState.results.length - 1);
                
                // Save to localStorage for backup
                localStorage.setItem('wayback-results', JSON.stringify({
                    results: appState.results,
                    timestamp: Date.now()
                }));
                
            } catch (error) {
                console.error(\`Error processing \${domain}:\`, error);
                const errorResult = {
                    domain,
                    snapshotCount: 0,
                    firstSnapshot: '',
                    lastSnapshot: '',
                    statusCodes: {},
                    error: error.message
                };
                
                appState.results.push(errorResult);
                addResultRow(errorResult, appState.results.length - 1);
            }
            
            appState.currentIndex++;
            updateProgress();
            
            // Rate limiting delay
            const delay = Math.ceil(1000 / CONFIG.RATE_LIMITS.search_calls_per_second);
            await new Promise(resolve => setTimeout(resolve, delay));
            
            return true;
        }

        async function startProcessing() {
            if (appState.processing) return;
            
            appState.processing = true;
            appState.paused = false;
            appState.startTime = Date.now();
            
            elements.statusText.textContent = 'Đang xử lý...';
            updateUI();
            
            showToast(\`Bắt đầu xử lý \${appState.validDomains.length} domain\`, 'info');
            
            while (appState.processing && await processNextDomain()) {
                // Continue processing
            }
            
            if (appState.currentIndex >= appState.validDomains.length) {
                appState.processing = false;
                elements.statusText.textContent = 'Hoàn thành';
                elements.progressText.textContent = 'Đã hoàn thành xử lý tất cả domain';
                showToast('Đã hoàn thành xử lý tất cả domain!', 'success');
            }
            
            updateUI();
        }

        function pauseProcessing() {
            appState.paused = true;
            elements.statusText.textContent = 'Đã tạm dừng';
            updateUI();
            showToast('Đã tạm dừng xử lý', 'warning');
        }

        function resumeProcessing() {
            appState.paused = false;
            elements.statusText.textContent = 'Đang xử lý...';
            updateUI();
            showToast('Tiếp tục xử lý', 'info');
            startProcessing();
        }

        function stopProcessing() {
            appState.processing = false;
            appState.paused = false;
            elements.statusText.textContent = 'Đã dừng';
            elements.progressText.textContent = 'Đã dừng xử lý';
            updateUI();
            showToast('Đã dừng xử lý', 'warning');
        }

        function clearAll() {
            elements.domainTextarea.value = '';
            appState = {
                domains: [],
                validDomains: [],
                invalidDomains: [],
                results: [],
                processing: false,
                paused: false,
                currentIndex: 0,
                queue: [],
                startTime: null
            };
            
            elements.resultsTableBody.innerHTML = \`
                <tr>
                    <td colspan="5" class="px-6 py-12 text-center text-gray-500">
                        Chưa có dữ liệu. Nhập domain và bắt đầu kiểm tra.
                    </td>
                </tr>
            \`;
            
            localStorage.removeItem('wayback-results');
            updateDomainCount();
            showToast('Đã xóa tất cả dữ liệu', 'info');
        }

        function copyToClipboard() {
            if (appState.results.length === 0) return;
            
            const headers = ['STT', 'Domain', 'Snapshot', 'Thời gian', 'StatusCode'];
            const rows = appState.results.map((result, index) => {
                const statusCodesText = Object.entries(result.statusCodes)
                    .map(([code, count]) => \`\${code}:\${count}\`)
                    .join(' - ') || 'N/A';
                
                const timeRange = result.firstSnapshot && result.lastSnapshot ? 
                    \`\${result.firstSnapshot} - \${result.lastSnapshot}\` : 'N/A';
                
                return [
                    index + 1,
                    result.domain,
                    result.error ? 'Error' : result.snapshotCount,
                    timeRange,
                    result.error || statusCodesText
                ];
            });
            
            const tsvContent = [headers, ...rows]
                .map(row => row.join('\\t'))
                .join('\\n');
            
            navigator.clipboard.writeText(tsvContent).then(() => {
                showToast('Đã copy vào clipboard', 'success');
            }).catch(() => {
                showToast('Không thể copy vào clipboard', 'error');
            });
        }

        function exportCSV() {
            if (appState.results.length === 0) return;
            
            const headers = ['STT', 'Domain', 'Snapshot', 'Thời gian', 'StatusCode'];
            const rows = appState.results.map((result, index) => {
                const statusCodesText = Object.entries(result.statusCodes)
                    .map(([code, count]) => \`\${code}:\${count}\`)
                    .join(' - ') || 'N/A';
                
                const timeRange = result.firstSnapshot && result.lastSnapshot ? 
                    \`\${result.firstSnapshot} - \${result.lastSnapshot}\` : 'N/A';
                
                return [
                    index + 1,
                    result.domain,
                    result.error ? 'Error' : result.snapshotCount,
                    timeRange,
                    result.error || statusCodesText
                ];
            });
            
            const csvContent = [headers, ...rows]
                .map(row => row.map(cell => \`"\${cell}"\`).join(','))
                .join('\\n');
            
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = \`wayback-results-\${new Date().toISOString().split('T')[0]}.csv\`;
            link.click();
            
            showToast('Đã export CSV', 'success');
        }

        function exportExcel() {
            showToast('Export Excel chưa được hỗ trợ trên Cloudflare Worker', 'warning');
        }

        function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            reader.onload = function(e) {
                const content = e.target.result;
                let domains = [];
                
                if (file.name.endsWith('.csv')) {
                    // Parse CSV
                    domains = content.split('\\n')
                        .map(line => line.split(',')[0].trim().replace(/"/g, ''))
                        .filter(domain => domain);
                } else {
                    // Parse TXT
                    domains = content.split('\\n')
                        .map(line => line.trim())
                        .filter(domain => domain);
                }
                
                elements.domainTextarea.value = domains.join('\\n');
                elements.fileInfo.classList.remove('hidden');
                elements.fileInfo.textContent = \`Đã tải \${domains.length} domain từ \${file.name}\`;
                
                updateDomainCount();
                showToast(\`Đã import \${domains.length} domain từ file\`, 'success');
            };
            
            reader.readAsText(file);
        }

        // Event listeners
        elements.domainTextarea.addEventListener('input', updateDomainCount);
        elements.fileUpload.addEventListener('change', handleFileUpload);
        elements.clearBtn.addEventListener('click', clearAll);
        elements.startBtn.addEventListener('click', startProcessing);
        elements.pauseBtn.addEventListener('click', pauseProcessing);
        elements.resumeBtn.addEventListener('click', resumeProcessing);
        elements.stopBtn.addEventListener('click', stopProcessing);
        elements.copyBtn.addEventListener('click', copyToClipboard);
        elements.exportCsvBtn.addEventListener('click', exportCSV);
        elements.exportExcelBtn.addEventListener('click', exportExcel);

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey) {
                if (e.key === 'v' && document.activeElement === elements.domainTextarea) {
                    setTimeout(updateDomainCount, 100);
                } else if (e.key === 'c' && !document.activeElement.matches('input, textarea')) {
                    e.preventDefault();
                    copyToClipboard();
                }
            }
        });

        // File drag and drop
        const fileUploadArea = elements.fileUpload.parentElement;
        fileUploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            fileUploadArea.classList.add('border-primary-500', 'bg-primary-50');
        });
        
        fileUploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            fileUploadArea.classList.remove('border-primary-500', 'bg-primary-50');
        });
        
        fileUploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            fileUploadArea.classList.remove('border-primary-500', 'bg-primary-50');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                elements.fileUpload.files = files;
                handleFileUpload({ target: { files } });
            }
        });

        // Load saved results on page load
        function loadSavedResults() {
            try {
                const saved = localStorage.getItem('wayback-results');
                if (saved) {
                    const data = JSON.parse(saved);
                    if (data.results && Array.isArray(data.results)) {
                        appState.results = data.results;
                        
                        elements.resultsTableBody.innerHTML = '';
                        data.results.forEach((result, index) => {
                            addResultRow(result, index);
                        });
                        
                        showToast(\`Đã khôi phục \${data.results.length} kết quả từ session trước\`, 'info');
                    }
                }
            } catch (error) {
                console.error('Error loading saved results:', error);
            }
        }

        // Initialize
        updateDomainCount();
        updateUI();
        loadSavedResults();
    `;
}

export default {
    async fetch(request, env, ctx) {
        const url = new URL(request.url);
        
        // Handle API routes
        if (url.pathname === '/api/wayback') {
            return handleWaybackAPI(request);
        }
        
        // Serve main HTML page
        return new Response(HTML_TEMPLATE, {
            headers: {
                'Content-Type': 'text/html;charset=UTF-8',
                'Cache-Control': 'public, max-age=3600'
            }
        });
    }
};

async function handleWaybackAPI(request) {
    const url = new URL(request.url);
    const domain = url.searchParams.get('domain');
    
    if (!domain) {
        return new Response(JSON.stringify({ error: 'Domain parameter required' }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
        });
    }
    
    try {
        // Validate domain
        const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$/;
        if (!domainRegex.test(domain)) {
            return new Response(JSON.stringify({ error: 'Invalid domain format' }), {
                status: 400,
                headers: { 'Content-Type': 'application/json' }
            });
        }
        
        // Make request to Wayback Machine API
        const waybackUrl = `http://web.archive.org/cdx/search/cdx?url=${domain}&output=json`;
        
        const response = await fetch(waybackUrl, {
            headers: {
                'User-Agent': 'Wayback Domain Checker/1.0'
            }
        });
        
        if (!response.ok) {
            if (response.status === 429) {
                return new Response(JSON.stringify({ error: 'Rate limit exceeded' }), {
                    status: 429,
                    headers: { 
                        'Content-Type': 'application/json',
                        'Retry-After': '2'
                    }
                });
            }
            
            throw new Error(`HTTP ${response.status}`);
        }
        
        const data = await response.text();
        
        // Parse JSON if possible
        try {
            const jsonData = JSON.parse(data);
            return new Response(JSON.stringify(jsonData), {
                headers: { 
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                }
            });
        } catch {
            // Return as array if not valid JSON
            return new Response(JSON.stringify([]), {
                headers: { 
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*'
                }
            });
        }
        
    } catch (error) {
        console.error('Wayback API error:', error);
        
        return new Response(JSON.stringify({ 
            error: error.message || 'Internal server error' 
        }), {
            status: 500,
            headers: { 'Content-Type': 'application/json' }
        });
    }
}
