/**
 * Export Helper Utility
 * Handles data export functionality for various formats
 */
export class ExportHelper {
    constructor() {
        this.supportedFormats = {
            csv: 'text/csv',
            tsv: 'text/tab-separated-values',
            json: 'application/json',
            xml: 'application/xml',
            txt: 'text/plain',
            xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        };

        this.csvOptions = {
            delimiter: ',',
            enclosure: '"',
            escape: '"',
            lineEnding: '\r\n',
            includeHeader: true,
            encoding: 'utf-8'
        };

        this.tsvOptions = {
            delimiter: '\t',
            enclosure: '',
            escape: '',
            lineEnding: '\r\n',
            includeHeader: true,
            encoding: 'utf-8'
        };
    }

    /**
     * Exports data to CSV format
     * @param {Array} data - Array of objects to export
     * @param {Object} options - Export options
     * @returns {string} CSV content
     */
    exportToCSV(data, options = {}) {
        const opts = { ...this.csvOptions, ...options };
        
        if (!Array.isArray(data) || data.length === 0) {
            return '';
        }

        const headers = opts.headers || this.extractHeaders(data[0]);
        const rows = [];

        // Add header row
        if (opts.includeHeader) {
            rows.push(this.formatCSVRow(headers, opts));
        }

        // Add data rows
        for (const item of data) {
            const row = headers.map(header => {
                const value = this.getNestedValue(item, header);
                return this.sanitizeCSVValue(value, opts);
            });
            rows.push(this.formatCSVRow(row, opts));
        }

        return rows.join(opts.lineEnding);
    }

    /**
     * Exports data to TSV format
     * @param {Array} data - Array of objects to export
     * @param {Object} options - Export options
     * @returns {string} TSV content
     */
    exportToTSV(data, options = {}) {
        const opts = { ...this.tsvOptions, ...options };
        return this.exportToCSV(data, opts);
    }

    /**
     * Exports data to JSON format
     * @param {Array} data - Array of objects to export
     * @param {Object} options - Export options
     * @returns {string} JSON content
     */
    exportToJSON(data, options = {}) {
        const opts = {
            pretty: true,
            indent: 2,
            ...options
        };

        try {
            if (opts.pretty) {
                return JSON.stringify(data, null, opts.indent);
            }
            return JSON.stringify(data);
        } catch (error) {
            throw new Error(`JSON export failed: ${error.message}`);
        }
    }

    /**
     * Exports data to XML format
     * @param {Array} data - Array of objects to export
     * @param {Object} options - Export options
     * @returns {string} XML content
     */
    exportToXML(data, options = {}) {
        const opts = {
            rootElement: 'data',
            itemElement: 'item',
            pretty: true,
            declaration: true,
            encoding: 'UTF-8',
            ...options
        };

        let xml = '';
        
        if (opts.declaration) {
            xml += `<?xml version="1.0" encoding="${opts.encoding}"?>\n`;
        }

        xml += `<${opts.rootElement}>\n`;

        for (const item of data) {
            xml += `  <${opts.itemElement}>\n`;
            xml += this.objectToXML(item, 4);
            xml += `  </${opts.itemElement}>\n`;
        }

        xml += `</${opts.rootElement}>`;

        return xml;
    }

    /**
     * Exports data to plain text format
     * @param {Array} data - Array of objects to export
     * @param {Object} options - Export options
     * @returns {string} Text content
     */
    exportToText(data, options = {}) {
        const opts = {
            format: 'table', // table, list, json
            separator: ' | ',
            padding: true,
            ...options
        };

        switch (opts.format) {
            case 'table':
                return this.formatAsTable(data, opts);
            case 'list':
                return this.formatAsList(data, opts);
            case 'json':
                return this.exportToJSON(data, { pretty: true });
            default:
                return this.formatAsTable(data, opts);
        }
    }

    /**
     * Creates and downloads a file
     * @param {string} content - File content
     * @param {string} filename - File name
     * @param {string} contentType - MIME type
     */
    downloadFile(content, filename, contentType) {
        try {
            // Create blob
            const blob = new Blob([content], { type: contentType });
            
            // Create download link
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            
            // Trigger download
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            // Clean up
            URL.revokeObjectURL(url);
            
        } catch (error) {
            throw new Error(`Download failed: ${error.message}`);
        }
    }

    /**
     * Copies content to clipboard
     * @param {string} content - Content to copy
     * @returns {Promise<boolean>} Success status
     */
    async copyToClipboard(content) {
        try {
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(content);
                return true;
            } else {
                // Fallback for older browsers
                return this.fallbackCopyToClipboard(content);
            }
        } catch (error) {
            console.error('Clipboard copy failed:', error);
            return false;
        }
    }

    /**
     * Fallback clipboard copy method
     * @param {string} content - Content to copy
     * @returns {boolean} Success status
     */
    fallbackCopyToClipboard(content) {
        try {
            const textArea = document.createElement('textarea');
            textArea.value = content;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            const result = document.execCommand('copy');
            document.body.removeChild(textArea);
            
            return result;
        } catch (error) {
            console.error('Fallback clipboard copy failed:', error);
            return false;
        }
    }

    /**
     * Formats wayback results for export
     * @param {Array} results - Array of domain results
     * @param {Object} options - Formatting options
     * @returns {Array} Formatted data for export
     */
    formatWaybackResults(results, options = {}) {
        const opts = {
            includeIndex: true,
            includeErrors: true,
            statusCodeFormat: 'detailed', // detailed, summary, count
            dateFormat: 'dd/mm/yyyy',
            ...options
        };

        return results.map((result, index) => {
            const formatted = {};

            if (opts.includeIndex) {
                formatted['STT'] = index + 1;
            }

            formatted['Domain'] = result.domain;
            formatted['Snapshot Count'] = result.error ? 'Error' : result.snapshotCount;
            
            // Format time range
            if (result.firstSnapshot && result.lastSnapshot) {
                formatted['First Snapshot'] = result.firstSnapshot;
                formatted['Last Snapshot'] = result.lastSnapshot;
                formatted['Time Range'] = result.firstSnapshot === result.lastSnapshot ? 
                    result.firstSnapshot : 
                    `${result.firstSnapshot} - ${result.lastSnapshot}`;
            } else {
                formatted['First Snapshot'] = 'N/A';
                formatted['Last Snapshot'] = 'N/A';
                formatted['Time Range'] = 'N/A';
            }

            // Format status codes
            if (result.error) {
                formatted['Status Codes'] = result.error;
                if (opts.includeErrors) {
                    formatted['Error'] = result.error;
                }
            } else {
                formatted['Status Codes'] = this.formatStatusCodes(result.statusCodes, opts.statusCodeFormat);
            }

            return formatted;
        });
    }

    /**
     * Formats status codes for display
     * @param {Object} statusCodes - Status code counts
     * @param {string} format - Format type
     * @returns {string} Formatted status codes
     */
    formatStatusCodes(statusCodes, format = 'detailed') {
        if (!statusCodes || Object.keys(statusCodes).length === 0) {
            return 'N/A';
        }

        const entries = Object.entries(statusCodes);

        switch (format) {
            case 'summary':
                return entries.map(([code, count]) => `${code}:${count}`).join(' - ');
            
            case 'count':
                return entries.reduce((total, [, count]) => total + count, 0).toString();
            
            case 'detailed':
            default:
                return entries
                    .sort(([a], [b]) => a.localeCompare(b))
                    .map(([code, count]) => `${code}:${count}`)
                    .join(' - ');
        }
    }

    /**
     * Generates filename with timestamp
     * @param {string} baseName - Base filename
     * @param {string} extension - File extension
     * @param {Object} options - Filename options
     * @returns {string} Generated filename
     */
    generateFilename(baseName, extension, options = {}) {
        const opts = {
            includeTimestamp: true,
            timestampFormat: 'YYYY-MM-DD_HH-mm-ss',
            separator: '_',
            ...options
        };

        let filename = baseName;

        if (opts.includeTimestamp) {
            const now = new Date();
            let timestamp;

            switch (opts.timestampFormat) {
                case 'YYYY-MM-DD':
                    timestamp = now.toISOString().split('T')[0];
                    break;
                case 'YYYY-MM-DD_HH-mm-ss':
                    timestamp = now.toISOString()
                        .replace(/T/, '_')
                        .replace(/:/g, '-')
                        .split('.')[0];
                    break;
                case 'timestamp':
                    timestamp = Math.floor(now.getTime() / 1000);
                    break;
                default:
                    timestamp = now.toISOString().split('T')[0];
            }

            filename += opts.separator + timestamp;
        }

        return filename + '.' + extension;
    }

    // Helper methods

    /**
     * Extracts headers from an object
     * @param {Object} obj - Object to extract headers from
     * @returns {Array} Array of header names
     */
    extractHeaders(obj) {
        if (!obj || typeof obj !== 'object') {
            return [];
        }
        return Object.keys(obj);
    }

    /**
     * Gets nested value from object using dot notation
     * @param {Object} obj - Object to get value from
     * @param {string} path - Dot notation path
     * @returns {*} Value at path
     */
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => 
            current && current[key] !== undefined ? current[key] : '', obj);
    }

    /**
     * Sanitizes value for CSV output
     * @param {*} value - Value to sanitize
     * @param {Object} options - CSV options
     * @returns {string} Sanitized value
     */
    sanitizeCSVValue(value, options) {
        if (value === null || value === undefined) {
            return '';
        }

        let str = String(value);

        // Handle special characters
        if (options.enclosure && (
            str.includes(options.delimiter) ||
            str.includes(options.enclosure) ||
            str.includes('\n') ||
            str.includes('\r')
        )) {
            // Escape enclosure characters
            str = str.replace(new RegExp(options.enclosure, 'g'), 
                options.escape + options.enclosure);
            
            // Wrap in enclosure
            str = options.enclosure + str + options.enclosure;
        }

        return str;
    }

    /**
     * Formats a CSV row
     * @param {Array} row - Row data
     * @param {Object} options - CSV options
     * @returns {string} Formatted row
     */
    formatCSVRow(row, options) {
        return row.join(options.delimiter);
    }

    /**
     * Converts object to XML
     * @param {Object} obj - Object to convert
     * @param {number} indent - Indentation level
     * @returns {string} XML string
     */
    objectToXML(obj, indent = 0) {
        let xml = '';
        const spaces = ' '.repeat(indent);

        for (const [key, value] of Object.entries(obj)) {
            const tagName = key.replace(/[^a-zA-Z0-9_-]/g, '_');
            
            if (value === null || value === undefined) {
                xml += `${spaces}<${tagName}/>\n`;
            } else if (typeof value === 'object' && !Array.isArray(value)) {
                xml += `${spaces}<${tagName}>\n`;
                xml += this.objectToXML(value, indent + 2);
                xml += `${spaces}</${tagName}>\n`;
            } else if (Array.isArray(value)) {
                xml += `${spaces}<${tagName}>\n`;
                for (const item of value) {
                    xml += this.objectToXML({ item }, indent + 2);
                }
                xml += `${spaces}</${tagName}>\n`;
            } else {
                const sanitizedValue = String(value)
                    .replace(/&/g, '&amp;')
                    .replace(/</g, '&lt;')
                    .replace(/>/g, '&gt;')
                    .replace(/"/g, '&quot;')
                    .replace(/'/g, '&apos;');
                
                xml += `${spaces}<${tagName}>${sanitizedValue}</${tagName}>\n`;
            }
        }

        return xml;
    }

    /**
     * Formats data as table
     * @param {Array} data - Data to format
     * @param {Object} options - Formatting options
     * @returns {string} Table formatted string
     */
    formatAsTable(data, options) {
        if (!Array.isArray(data) || data.length === 0) {
            return '';
        }

        const headers = options.headers || this.extractHeaders(data[0]);
        const rows = data.map(item => 
            headers.map(header => String(this.getNestedValue(item, header)))
        );

        // Calculate column widths
        const widths = headers.map((header, index) => {
            const headerWidth = header.length;
            const dataWidth = Math.max(...rows.map(row => row[index] ? row[index].length : 0));
            return Math.max(headerWidth, dataWidth);
        });

        // Format header
        let table = '';
        if (options.includeHeader !== false) {
            table += headers.map((header, index) => 
                options.padding ? header.padEnd(widths[index]) : header
            ).join(options.separator) + '\n';
            
            // Add separator line
            table += widths.map(width => '-'.repeat(width)).join(options.separator) + '\n';
        }

        // Format rows
        for (const row of rows) {
            table += row.map((cell, index) => 
                options.padding ? cell.padEnd(widths[index]) : cell
            ).join(options.separator) + '\n';
        }

        return table;
    }

    /**
     * Formats data as list
     * @param {Array} data - Data to format
     * @param {Object} options - Formatting options
     * @returns {string} List formatted string
     */
    formatAsList(data, options) {
        let list = '';
        
        for (let i = 0; i < data.length; i++) {
            const item = data[i];
            list += `Item ${i + 1}:\n`;
            
            for (const [key, value] of Object.entries(item)) {
                list += `  ${key}: ${value}\n`;
            }
            
            if (i < data.length - 1) {
                list += '\n';
            }
        }

        return list;
    }

    /**
     * Estimates file size
     * @param {string} content - Content to estimate
     * @param {string} encoding - Text encoding
     * @returns {Object} Size information
     */
    estimateFileSize(content, encoding = 'utf-8') {
        const encoder = new TextEncoder();
        const bytes = encoder.encode(content).length;
        
        return {
            bytes,
            kb: (bytes / 1024).toFixed(2),
            mb: (bytes / (1024 * 1024)).toFixed(2),
            readable: bytes < 1024 ? `${bytes} B` :
                     bytes < 1024 * 1024 ? `${(bytes / 1024).toFixed(1)} KB` :
                     `${(bytes / (1024 * 1024)).toFixed(1)} MB`
        };
    }
}
