#!/usr/bin/env node

/**
 * Deployment Script for Wayback Machine Snapshot Checker
 * Prepares and deploys the application to Cloudflare Workers
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class Deployer {
    constructor() {
        this.projectRoot = process.cwd();
        this.srcDir = path.join(this.projectRoot, 'src');
        this.buildDir = path.join(this.projectRoot, 'dist');
        
        console.log('🚀 Wayback Machine Checker - Deployment Script');
        console.log('================================================');
    }

    /**
     * Main deployment process
     */
    async deploy() {
        try {
            this.validateEnvironment();
            this.createBuildDirectory();
            this.bundleApplication();
            this.generateManifest();
            this.runTests();
            this.deployToCloudflare();
            this.showSuccess();
            
        } catch (error) {
            console.error('❌ Deployment failed:', error.message);
            process.exit(1);
        }
    }

    /**
     * Validates deployment environment
     */
    validateEnvironment() {
        console.log('🔍 Validating environment...');
        
        // Check if we're in the right directory
        if (!fs.existsSync(path.join(this.projectRoot, 'wrangler.toml'))) {
            throw new Error('wrangler.toml not found. Are you in the project root?');
        }

        // Check if Wrangler CLI is installed
        try {
            execSync('wrangler --version', { stdio: 'pipe' });
            console.log('✅ Wrangler CLI found');
        } catch (error) {
            throw new Error('Wrangler CLI not found. Install with: npm install -g wrangler');
        }

        // Check if user is logged in
        try {
            execSync('wrangler whoami', { stdio: 'pipe' });
            console.log('✅ Wrangler authentication verified');
        } catch (error) {
            throw new Error('Not logged in to Wrangler. Run: wrangler login');
        }

        // Check source files
        const requiredFiles = [
            'src/index.js',
            'src/utils/DomainValidator.js',
            'src/utils/DateFormatter.js',
            'src/utils/ExportHelper.js',
            'src/services/WaybackAPI.js',
            'src/services/RateLimiter.js',
            'src/services/DataProcessor.js',
            'src/components/DomainInput.js',
            'src/components/ProgressBar.js',
            'src/components/ResultsTable.js',
            'src/components/ExportButtons.js',
            'src/AppManager.js'
        ];

        for (const file of requiredFiles) {
            if (!fs.existsSync(path.join(this.projectRoot, file))) {
                throw new Error(`Required file missing: ${file}`);
            }
        }

        console.log('✅ All required files found');
    }

    /**
     * Creates build directory
     */
    createBuildDirectory() {
        console.log('📁 Creating build directory...');
        
        if (fs.existsSync(this.buildDir)) {
            fs.rmSync(this.buildDir, { recursive: true });
        }
        
        fs.mkdirSync(this.buildDir, { recursive: true });
        console.log('✅ Build directory created');
    }

    /**
     * Bundles the application for deployment
     */
    bundleApplication() {
        console.log('📦 Bundling application...');
        
        // Read all source files and create bundle
        const bundle = this.createBundle();
        
        // Write bundled index.js
        const bundlePath = path.join(this.buildDir, 'index.js');
        fs.writeFileSync(bundlePath, bundle, 'utf8');
        
        // Copy wrangler.toml
        const wranglerPath = path.join(this.projectRoot, 'wrangler.toml');
        const destWranglerPath = path.join(this.buildDir, 'wrangler.toml');
        fs.copyFileSync(wranglerPath, destWranglerPath);
        
        // Copy package.json
        const packagePath = path.join(this.projectRoot, 'package.json');
        if (fs.existsSync(packagePath)) {
            const destPackagePath = path.join(this.buildDir, 'package.json');
            fs.copyFileSync(packagePath, destPackagePath);
        }

        console.log('✅ Application bundled');
    }

    /**
     * Creates application bundle
     * @returns {string} Bundled application code
     */
    createBundle() {
        // Read main index.js
        const indexPath = path.join(this.srcDir, 'index.js');
        let mainContent = fs.readFileSync(indexPath, 'utf8');

        // Since we're using embedded HTML in index.js and client-side modules,
        // we don't need complex bundling for Cloudflare Workers
        // Just return the main content as-is
        return mainContent;
    }

    /**
     * Generates deployment manifest
     */
    generateManifest() {
        console.log('📄 Generating deployment manifest...');
        
        const manifest = {
            name: 'wayback-snapshot-checker',
            version: '1.0.0',
            description: 'Wayback Machine Snapshot Checker for Cloudflare Workers',
            author: 'Your Name',
            deployed: new Date().toISOString(),
            files: {
                main: 'index.js',
                config: 'wrangler.toml'
            },
            features: {
                rateLimiting: true,
                batchProcessing: true,
                fileUpload: true,
                multipleExports: true,
                progressTracking: true,
                errorHandling: true,
                stateManagement: true
            },
            limits: {
                maxDomains: 5000,
                requestsPerSecond: 0.8,
                maxFileSize: '10MB',
                supportedFormats: ['txt', 'csv', 'tsv', 'json']
            }
        };

        const manifestPath = path.join(this.buildDir, 'manifest.json');
        fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2), 'utf8');
        
        console.log('✅ Deployment manifest generated');
    }

    /**
     * Runs pre-deployment tests
     */
    runTests() {
        console.log('🧪 Running pre-deployment tests...');
        
        try {
            // Basic syntax check
            const indexPath = path.join(this.buildDir, 'index.js');
            const content = fs.readFileSync(indexPath, 'utf8');
            
            // Check for common issues
            if (!content.includes('addEventListener("fetch"')) {
                throw new Error('Missing fetch event listener in main script');
            }

            if (!content.includes('Response(')) {
                throw new Error('Missing Response constructor in main script');
            }

            // Check file sizes
            const stats = fs.statSync(indexPath);
            const sizeKB = Math.round(stats.size / 1024);
            console.log(`📊 Bundle size: ${sizeKB} KB`);
            
            if (stats.size > 1024 * 1024) { // 1MB limit for CF Workers
                console.warn('⚠️ Bundle size is large, consider optimization');
            }

            console.log('✅ Pre-deployment tests passed');
            
        } catch (error) {
            throw new Error(`Pre-deployment test failed: ${error.message}`);
        }
    }

    /**
     * Deploys to Cloudflare Workers
     */
    deployToCloudflare() {
        console.log('🌐 Deploying to Cloudflare Workers...');
        
        try {
            // Change to build directory
            process.chdir(this.buildDir);
            
            // Deploy using Wrangler
            console.log('📤 Running wrangler deploy...');
            const output = execSync('wrangler deploy', { 
                encoding: 'utf8',
                stdio: ['inherit', 'pipe', 'pipe']
            });
            
            console.log(output);
            
            // Extract deployment URL from output
            const urlMatch = output.match(/https:\/\/[^\s]+/);
            if (urlMatch) {
                this.deploymentUrl = urlMatch[0];
                console.log(`✅ Deployed to: ${this.deploymentUrl}`);
            }
            
            // Change back to project root
            process.chdir(this.projectRoot);
            
        } catch (error) {
            process.chdir(this.projectRoot);
            throw new Error(`Cloudflare deployment failed: ${error.message}`);
        }
    }

    /**
     * Shows deployment success message
     */
    showSuccess() {
        console.log('\n🎉 Deployment Successful!');
        console.log('========================');
        
        if (this.deploymentUrl) {
            console.log(`🌐 Application URL: ${this.deploymentUrl}`);
            console.log(`🧪 Test URL: ${this.deploymentUrl}?test=true`);
        }
        
        console.log('\n📋 Post-Deployment Checklist:');
        console.log('- ✅ Test the application with a few domains');
        console.log('- ✅ Verify rate limiting is working (0.8 req/sec)');
        console.log('- ✅ Test file upload functionality');
        console.log('- ✅ Test export functionality (CSV, TSV, JSON)');
        console.log('- ✅ Verify error handling with invalid domains');
        console.log('- ✅ Test progress tracking and pause/resume');
        
        console.log('\n📚 Usage Instructions:');
        console.log('1. Enter domains manually or upload a TXT/CSV file');
        console.log('2. Click "Start Processing" to begin checking snapshots');
        console.log('3. Monitor progress with the real-time progress bar');
        console.log('4. Export results in CSV, TSV, or JSON format');
        console.log('5. Use pause/resume for long-running jobs');
        
        console.log('\n🔧 Technical Details:');
        console.log('- Rate Limited: 0.8 requests/second (Wayback Machine compliance)');
        console.log('- Max Domains: 5,000 per batch');
        console.log('- Supported Formats: TXT, CSV upload | CSV, TSV, JSON export');
        console.log('- Progress Tracking: Real-time with ETA calculation');
        console.log('- Error Handling: Comprehensive with retry logic');
        console.log('- State Management: Auto-save/restore in localStorage');
        
        console.log('\n🚀 Deployment Complete!');
    }
}

// Run deployment if called directly
if (require.main === module) {
    const deployer = new Deployer();
    deployer.deploy().catch(error => {
        console.error('Deployment failed:', error);
        process.exit(1);
    });
}

module.exports = Deployer;
