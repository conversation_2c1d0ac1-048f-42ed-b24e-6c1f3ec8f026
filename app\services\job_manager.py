"""
Job management service for tracking domain checking jobs
"""
import asyncio
from typing import Dict, List, Optional
from datetime import datetime
import threading

from app.models.domain_models import (
    JobProgress, JobStatus, BatchInfo, DomainCheckResults, DomainResult
)
from app.services.csv_service import CSVProcessor
from app.routers.websocket_handler import send_job_progress_update, send_job_completion, send_job_error
from app.utils.logger import get_logger

logger = get_logger(__name__)

class JobManager:
    """Manages domain checking jobs and their progress"""
    
    def __init__(self):
        self.jobs: Dict[str, JobProgress] = {}
        self.results_files: Dict[str, str] = {}
        self.csv_processor = CSVProcessor()
        self._lock = threading.Lock()
    
    def create_job(self, job_id: str, job_progress: JobProgress):
        """Create a new job"""
        with self._lock:
            self.jobs[job_id] = job_progress
            logger.info(f"Created job {job_id}")
    
    def get_job_progress(self, job_id: str) -> Optional[JobProgress]:
        """Get job progress by ID"""
        with self._lock:
            return self.jobs.get(job_id)
    
    def update_job_status(self, job_id: str, status: JobStatus, error_message: str = None):
        """Update job status"""
        with self._lock:
            if job_id in self.jobs:
                self.jobs[job_id].status = status
                if error_message:
                    self.jobs[job_id].error_message = error_message
                
                if status == JobStatus.COMPLETED:
                    self.jobs[job_id].end_time = datetime.now().isoformat()
                    self.jobs[job_id].progress_percentage = 100.0
                elif status == JobStatus.FAILED:
                    self.jobs[job_id].end_time = datetime.now().isoformat()
                
                logger.info(f"Updated job {job_id} status to {status}")
                
                # Send WebSocket update
                asyncio.create_task(self._send_progress_update(job_id))
    
    def update_current_batch(self, job_id: str, batch_info: BatchInfo):
        """Update current batch information"""
        with self._lock:
            if job_id in self.jobs:
                self.jobs[job_id].current_batch = batch_info
                logger.info(f"Updated current batch for job {job_id}: {batch_info.batch_id}")
                
                # Send WebSocket update
                asyncio.create_task(self._send_progress_update(job_id))
    
    def update_batch_completed(self, job_id: str, batch_info: BatchInfo):
        """Update when a batch is completed"""
        with self._lock:
            if job_id in self.jobs:
                job = self.jobs[job_id]
                job.completed_batches += 1
                job.processed_domains += batch_info.processed_domains
                job.current_batch = batch_info
                
                # Calculate progress percentage
                job.progress_percentage = (job.completed_batches / job.total_batches) * 100
                
                # Estimate remaining time
                if job.completed_batches > 0:
                    elapsed_time = self._calculate_elapsed_time(job.start_time)
                    avg_time_per_batch = elapsed_time / job.completed_batches
                    remaining_batches = job.total_batches - job.completed_batches
                    job.estimated_time_remaining = int(avg_time_per_batch * remaining_batches)
                
                logger.info(f"Batch completed for job {job_id}: {job.completed_batches}/{job.total_batches}")
                
                # Send WebSocket update
                asyncio.create_task(self._send_progress_update(job_id))
    
    def set_results_file(self, job_id: str, file_path: str):
        """Set the results file path for a job"""
        with self._lock:
            self.results_files[job_id] = file_path
            if job_id in self.jobs:
                self.jobs[job_id].results_file_url = f"/api/job/{job_id}/download"
                logger.info(f"Set results file for job {job_id}: {file_path}")
    
    def get_results_file_path(self, job_id: str) -> Optional[str]:
        """Get the results file path for a job"""
        with self._lock:
            return self.results_files.get(job_id)
    
    def cancel_job(self, job_id: str) -> bool:
        """Cancel a job"""
        with self._lock:
            if job_id in self.jobs:
                job = self.jobs[job_id]
                if job.status in [JobStatus.PENDING, JobStatus.PROCESSING]:
                    job.status = JobStatus.CANCELLED
                    job.end_time = datetime.now().isoformat()
                    logger.info(f"Cancelled job {job_id}")
                    
                    # Send WebSocket update
                    asyncio.create_task(self._send_progress_update(job_id))
                    return True
            return False
    
    def list_jobs(self) -> List[JobProgress]:
        """List all jobs"""
        with self._lock:
            return list(self.jobs.values())
    
    async def get_job_results(self, job_id: str) -> Optional[DomainCheckResults]:
        """Get detailed job results"""
        with self._lock:
            if job_id not in self.jobs:
                return None
            
            job = self.jobs[job_id]
            if job.status != JobStatus.COMPLETED:
                return None
            
            file_path = self.results_files.get(job_id)
            if not file_path:
                return None
        
        try:
            # Parse results from CSV file
            results = await self.csv_processor.parse_results_file(file_path)
            
            # Calculate processing time
            processing_time = None
            if job.start_time and job.end_time:
                start = datetime.fromisoformat(job.start_time)
                end = datetime.fromisoformat(job.end_time)
                processing_time = (end - start).total_seconds()
            
            return DomainCheckResults(
                job_id=job_id,
                job_name=job.job_name,
                total_domains=job.total_domains,
                processed_domains=job.processed_domains,
                results=results,
                status=job.status,
                start_time=job.start_time,
                end_time=job.end_time,
                processing_time=processing_time,
                download_url=job.results_file_url
            )
            
        except Exception as e:
            logger.error(f"Error getting job results for {job_id}: {str(e)}")
            return None
    
    def _calculate_elapsed_time(self, start_time: str) -> float:
        """Calculate elapsed time in seconds"""
        try:
            start = datetime.fromisoformat(start_time)
            now = datetime.now()
            return (now - start).total_seconds()
        except Exception:
            return 0.0
    
    async def _send_progress_update(self, job_id: str):
        """Send progress update via WebSocket"""
        try:
            job = self.get_job_progress(job_id)
            if job:
                progress_data = {
                    "job_id": job_id,
                    "status": job.status,
                    "progress_percentage": job.progress_percentage,
                    "processed_domains": job.processed_domains,
                    "total_domains": job.total_domains,
                    "completed_batches": job.completed_batches,
                    "total_batches": job.total_batches,
                    "estimated_time_remaining": job.estimated_time_remaining,
                    "current_batch": job.current_batch.dict() if job.current_batch else None
                }
                
                if job.status == JobStatus.COMPLETED:
                    await send_job_completion(job_id, progress_data)
                elif job.status == JobStatus.FAILED:
                    await send_job_error(job_id, {
                        **progress_data,
                        "error_message": job.error_message
                    })
                else:
                    await send_job_progress_update(job_id, progress_data)
                    
        except Exception as e:
            logger.error(f"Error sending progress update for job {job_id}: {str(e)}")
